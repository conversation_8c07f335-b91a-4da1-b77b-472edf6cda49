<package>
  <name>yocs_velocity_smoother</name>
  <version>0.12.1</version>
  <description>
     Bound incoming velocity messages according to robot velocity and acceleration limits.
  </description>
  <author><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON><PERSON><PERSON></maintainer>
  <license>BSD</license>
  <url type="website">http://ros.org/wiki/yocs_velocity_smoother</url>
  <url type="repository">https://github.com/yujinrobot/yujin_ocs</url>
  <url type="bugtracker">https://github.com/yujinrobot/yujin_ocs/issues</url>
  
  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>roscpp</build_depend>
  <build_depend>pluginlib</build_depend>
  <build_depend>nodelet</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>ecl_threads</build_depend>
  <build_depend>dynamic_reconfigure</build_depend>

  <run_depend>roscpp</run_depend>
  <run_depend>pluginlib</run_depend>
  <run_depend>nodelet</run_depend>
  <run_depend>geometry_msgs</run_depend>
  <run_depend>nav_msgs</run_depend>
  <run_depend>ecl_threads</run_depend>
  <run_depend>dynamic_reconfigure</run_depend>
  
  <export>
    <nodelet plugin="${prefix}/plugins/nodelets.xml"/>
  </export>
</package>
