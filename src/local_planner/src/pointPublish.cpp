#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <ros/ros.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/Point.h>
#include <nav_msgs/Odometry.h>
#include <tf/transform_datatypes.h>
#include <tf/transform_broadcaster.h>
#include <local_planner/NavigationResult.h>
#include <local_planner/NavigationTarget.h>
#include <sstream>
#include <fstream>
#include <vector>
#include <termios.h>
#include <unistd.h>
#include <fcntl.h>
#include <std_msgs/Int8.h>
#include <iostream>
#include <thread>
#include <chrono>

using namespace std;

int point_id, point_info, gait, speed, manner, obsmode, navmode, delayTime;
string file_name;
vector<geometry_msgs::Point> points;
int mode = 0, num = 0;
bool init_flag = false;
ros::Publisher pubTarget;
ros::Publisher pubArriveStatus;
ros::Subscriber subResult;
ros::Subscriber subOdometry;
ros::Subscriber subArriveStatus;
ros::Subscriber subFloorStatus;
double odomx = 0, odomy = 0, odomz = 0;
std_msgs::Int8 floorStatus;
std_msgs::Int8 ArriveStatus;
std_msgs::Int8 Stop;
bool floor1_receive = false;
bool floor2_receive = false;
int stopPoint;
int stopPoint_old = 0;

// int value = 3;
/*
void resultHandler(const local_planner::NavigationResult::ConstPtr& Result)//终点位姿信息以及导航参数回调函数
{
  if (Result->nav_state == 0 && mode == 2 && init_flag)
  {
    local_planner::NavigationTarget target_point;
    target_point.pose_x = points[num].x;
    target_point.pose_y = points[num].y;
    target_point.pose_z = points[num].z;
    target_point.yaw = 0;
    target_point.nav_mode = 1;
    target_point.point_id = point_id;
    target_point.point_info = point_info;
    target_point.gait = gait;
    target_point.speed = speed;
    target_point.manner = manner;
    target_point.obsmode = obsmode;
    target_point.navmode = navmode;
    pubTarget.publish(target_point);
    std::cout<<"***********目标点已下发***********"<<std::endl;
    init_flag = true;
    num++;
    if (num == points.size()-1)
    {
      std::cout<<"***********任务已完成***********"<<std::endl;
      init_flag = false;
    }
  }
}

*/

// 定义一个名为 floorStatusHandler 的函数，用于处理楼层状态
void floorStatusHandler(const std_msgs::Int8::ConstPtr &status)
{
  // 将传入的楼层状态赋值给全局变量 floorStatus
  floorStatus.data = status->data;
       
   // 检查接收到的停止信号和电梯状态
   if (Stop.data == 1 && (floorStatus.data == 1 || floorStatus.data == 2)) // 接收电梯状态，电梯到达
    if (floorStatus.data ==2 || !floor1_receive)
   {
     floor1_receive = true ;
     if(floorStatus.data == 2 && !floor2_receive)
     {
      // for(int delayCounter = 1;delayCounter < delayTime; delayCounter ++);
      std::this_thread::sleep_for(std::chrono::seconds(delayTime));
      floor2_receive = true ;
     }
     // 创建一个PoseStamped消息用于存储目标点信息
     geometry_msgs::PoseStamped point;
     // 设置时间戳为当前时间
     point.header.stamp = ros::Time::now();
     // 设置参考坐标系为"map"
     point.header.frame_id = "map";
     // 设置目标点的位置信息
     point.pose.position.x = points[num].x;
     point.pose.position.y = points[num].y;
     point.pose.position.z = points[num].z;
     // 设置目标点的朝向为0度（即朝向北方）
     point.pose.orientation = tf::createQuaternionMsgFromYaw(0);
     // 发布目标点信息
     pubTarget.publish(point);
     Stop.data == 0;
     // 输出目标点已下发的提示信息
     std::cout << "***********目标点已下发***********" << std::endl;
     // 设置初始化标志为true
     init_flag = true;
     // 检查是否到达最后一个目标点
     if (num == points.size() - 1)
     {
       // 输出任务已完成的提示信息
       std::cout << "***********任务已完成***********" << std::endl;
       // 设置初始化标志为false
       init_flag = false;
     }
   }


    // 检查电梯状态
     if (floorStatus.data == 1) // 如果电梯状态为1即电梯到达载体层，则重置电梯标志位
     {
       //
       floorStatus.data = 0;
     }
     if (floorStatus.data == 2) // 如果电梯状态为1即电梯到达载体层，则重置电梯标志位
     {
       //
       floorStatus.data = 3;
     }
  
}

void resultHandler(const std_msgs::Int8::ConstPtr &stop) // 终点位姿信息以及导航参数回调函数
{
  // 发布当前到达的目标点编号
  ArriveStatus.data = num + 1;
  stopPoint = num + 1;
  //ArriveStatus.data = num;
  pubArriveStatus.publish(ArriveStatus);
  Stop.data = stop->data;
  if (stopPoint != stopPoint_old)
  {
     floor1_receive = false;
  }
  
  stopPoint_old = stopPoint;

  // 增加目标点编号
  num++;
  // 检查接收到的停止信号和电梯状态
  if (stop->data == 1 && floorStatus.data == 3) // 接收电梯状态，电梯到达
  {
    // 创建一个PoseStamped消息用于存储目标点信息
    geometry_msgs::PoseStamped point;
    // 设置时间戳为当前时间
    point.header.stamp = ros::Time::now();
    // 设置参考坐标系为"map"
    point.header.frame_id = "map";
    // 设置目标点的位置信息
    point.pose.position.x = points[num].x;
    point.pose.position.y = points[num].y;
    point.pose.position.z = points[num].z;
    // 设置目标点的朝向为0度（即朝向北方）
    point.pose.orientation = tf::createQuaternionMsgFromYaw(0);
    // 发布目标点信息
    pubTarget.publish(point);
    stop->data == 0;
    // 输出目标点已下发的提示信息 
    std::cout << "***********目标点已下发***********" << std::endl;
    // 设置初始化标志为true
    init_flag = true;
    // 检查是否到达最后一个目标点
    if (num == points.size() - 1)
    {
      // 输出任务已完成的提示信息
      std::cout << "***********任务已完成***********" << std::endl;
      // 设置初始化标志为false
      init_flag = false;
    }
    floorStatus.data == 3;



  }
}

void odomHandler(const nav_msgs::Odometry::ConstPtr &odom) // 机器人位姿信息回调函数
{
  odomx = odom->pose.pose.position.x;
  odomy = odom->pose.pose.position.y;
  odomz = odom->pose.pose.position.z;
}

void readPointsFromFile(const std::string &filename)
{
  std::ifstream file(filename);
  if (!file.is_open())
  {
    ROS_ERROR("Failed to open file: %s", filename.c_str());
    return;
  }
  double x, y, z;
  while (file >> x >> y >> z)
  {
    geometry_msgs::Point point;
    point.x = x;
    point.y = y;
    point.z = z;
    points.push_back(point);
  }
  file.close();
  ROS_INFO("Loaded %d points from file.", points.size());
}

// 函数声明：kbhit() 用于检测键盘是否有按键按下
int kbhit()
{
  // 定义两个termios结构体变量oldt和newt，用于保存和设置终端属性
  struct termios oldt, newt;
  // 定义一个整型变量ch，用于存储读取到的字符
  int ch;
  // 定义一个整型变量oldf，用于保存和设置文件描述符的标志
  int oldf;
  // 获取当前终端的属性，并保存到oldt中
  tcgetattr(STDIN_FILENO, &oldt);
  // 将oldt的值复制给newt，以便后续修改
  newt = oldt;
  // 修改newt的c_lflag属性，禁用ICANON（规范模式）和ECHO（回显）
  newt.c_lflag &= ~(ICANON | ECHO);
  // 设置终端的属性为newt，即禁用规范模式和回显
  tcsetattr(STDIN_FILENO, TCSANOW, &newt);
  // 获取标准输入文件描述符的当前标志，并保存到oldf中
  oldf = fcntl(STDIN_FILENO, F_GETFL, 0);
  // 设置标准输入文件描述符的标志为非阻塞模式
  fcntl(STDIN_FILENO, F_SETFL, oldf | O_NONBLOCK);
  // 尝试从标准输入读取一个字符，如果无字符可读则返回EOF
  ch = getchar();
  // 恢复终端的属性为oldt，即恢复之前的设置
  tcsetattr(STDIN_FILENO, TCSANOW, &oldt);
  // 恢复标准输入文件描述符的标志为oldf，即恢复之前的设置
  fcntl(STDIN_FILENO, F_SETFL, oldf);
  // 如果读取到的字符不是EOF，说明有按键按下
  if (ch != EOF)
  {
    // 将读取到的字符重新放回标准输入缓冲区
    ungetc(ch, stdin);
    // 返回1，表示有按键按下
    return 1;
  }
  // 返回0，表示没有按键按下
  return 0;
}

int main(int argc, char **argv)
{
  ros::init(argc, argv, "pointPublish");
  ros::NodeHandle nh;
  ros::NodeHandle private_nh("~");
  private_nh.param("point_id", point_id, 0);
  private_nh.param("point_info", point_info, 0);
  private_nh.param("gait", gait, 0);
  private_nh.param("speed", speed, 1);
  private_nh.param("manner", manner, 0);
  private_nh.param("obsmode", obsmode, 1);
  private_nh.param("navmode", navmode, 0);
  private_nh.param("delayTime", delayTime, 2);
  private_nh.param("filename", file_name, std::string("points.txt"));
  
  floorStatus.data = 0;
  // 发布导航目标点信息
  // pubTarget = nh.advertise<local_planner::NavigationTarget> ("/navigation_target", 1);
  pubTarget = nh.advertise<geometry_msgs::PoseStamped>("/web_goal_pose", 1);
  // 订阅导航结果
  // subResult = nh.subscribe<local_planner::NavigationResult> ("/navigation_result", 1, resultHandler);
  subResult = nh.subscribe<std_msgs::Int8>("/stop", 1, resultHandler);
  // 订阅里程计信息
  subOdometry = nh.subscribe<nav_msgs::Odometry>("/state_estimation", 1, odomHandler);
  // pubResultPointer = &pubResult;
  subFloorStatus = nh.subscribe<std_msgs::Int8>("/arriveFloor", 1, floorStatusHandler);
  // 发布载体到达目标点信息
  pubArriveStatus = nh.advertise<std_msgs::Int8>("/stopAtTarget", 1);
  sleep(1);
  std::ofstream file;
  // std::cout << "请选择工作模式: 1.打点模式 2.巡检模式" << std::endl;
  // std::cin >> mode;
  mode = 2;
  if (mode == 2)
  {
    readPointsFromFile(file_name);
    for (int i = 0; i < points.size(); i++)
    {
      std::cout << "point" << i << " x: " << points[i].x << " y: " << points[i].y << " z: " << points[i].z << std::endl;
    }
    // local_planner::NavigationTarget target_point;
    // target_point.pose_x = points[num].x;
    // target_point.pose_y = points[num].y;
    // target_point.pose_z = points[num].z;
    // target_point.yaw = 0;
    // target_point.nav_mode = 1;
    // target_point.point_id = point_id;
    // target_point.point_info = point_info;
    // target_point.gait = gait;
    // target_point.speed = speed;
    // target_point.manner = manner;
    // target_point.obsmode = obsmode;
    // target_point.navmode = navmode;
    // pubTarget.publish(target_point);
    geometry_msgs::PoseStamped point;
    point.header.stamp = ros::Time::now();
    point.header.frame_id = "map";
    point.pose.position.x = points[num].x;
    point.pose.position.y = points[num].y;
    point.pose.position.z = points[num].z;
    point.pose.orientation = tf::createQuaternionMsgFromYaw(0);
    pubTarget.publish(point);
    std::cout << "***********目标点已下发***********" << std::endl;
      // 发布当前到达的目标点编号
   // ArriveStatus.data = 1;
   // pubArriveStatus.publish(ArriveStatus);
    init_flag = true;
    // num++;
  }
  else if (mode == 1)
  {
    init_flag = true;

    file.open(file_name);
    if (!file)
    {
      ROS_ERROR("Failed to open file!");
    }
  }
  ros::Rate rate(100);
  bool status = ros::ok();
  while (status)
  {
    ros::spinOnce();
    if (mode == 1 && init_flag)
    {
      if (kbhit())
      {
        char c = getchar();
        if (c == ' ')
        {
          if (file.is_open())
          {
            file << odomx << " " << odomy << " " << odomz << std::endl;
            std::cout << "***********当前点已记录***********" << std::endl;
            std::cout << "point" << " x: " << odomx << " y: " << odomy << " z: " << odomz << std::endl;
          }
          else
          {
            ROS_ERROR("Failed to open file!");
          }
        }
        else if (c == 'n')
        {
          init_flag = false;
          std::cout << "***********记录结束***********" << std::endl;
          if (file.is_open())
          {
            file.close();
          }
        }
      }
    }
    rate.sleep();
    status = ros::ok();
  }
  return 0;
}
