#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <ros/ros.h>
#include <message_filters/subscriber.h>
#include <message_filters/synchronizer.h>
#include <message_filters/sync_policies/approximate_time.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Float32.h>
#include <nav_msgs/Path.h>
#include <std_msgs/Float32MultiArray.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Int64.h>
#include <nav_msgs/Odometry.h>
#include <geometry_msgs/PointStamped.h>
#include <geometry_msgs/PolygonStamped.h>
#include <sensor_msgs/Imu.h>
#include <sensor_msgs/PointCloud2.h>
#include <sensor_msgs/Joy.h>
#include <tf/transform_datatypes.h>
#include <tf/transform_broadcaster.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/common/time.h>
#include <pcl/registration/icp.h>
#include <pcl/io/pcd_io.h>
#include <local_planner/NavigationResult.h>
#include <local_planner/NavigationTarget.h>

using namespace std;

const double PI = 3.1415926;

#define PLOTPATHSET 1

string pathFolder;
std_msgs::Int8 arrive_inf;
double vehicleLength = 0.6;
double vehicleWidth = 0.6;
double sensorOffsetX = 0;
double sensorOffsetY = 0;
bool twoWayDrive = false;
double laserVoxelSize = 0.05;
double terrainVoxelSize = 0.2;
bool useTerrainAnalysis = false;
bool checkRotObstacle = false;
double adjacentRange = 3.5;
double obstacleHeightThre = 0.2;
double groundHeightThre = 0.1;
double costHeightThre = 0.1;
double costScore = 0.02;
bool useCost = false;
const int laserCloudStackNum = 1;
int laserCloudCount = 0;
int pointPerPathThre = 2;
double minRelZ = -0.5;
double maxRelZ = 0.25;
double dirWeight = 0.02;
double dirThre = 90.0;
bool dirToVehicle = false;
double pathScale = 1.0;
double minPathScale = 0.75;
double pathScaleStep = 0.25;
bool pathScaleBySpeed = true;
double minPathRange = 1.0;
double pathRangeStep = 0.5;
bool pathRangeBySpeed = true;
bool pathCropByGoal = true;
double goalClearRange = 0.5;
double arrived_dis_threshold = 0.35;
double goalX = 0;
double goalY = 0;
double goalZ = 0;
std_msgs::Bool adjustmode;
int nav_start = 0;
int info = 0; //默认使用精调
//int obs_mode = 0;
int id = 0;
double maxSpeed = 0.8;
double targetX = 0;
double targetY = 0;
double targetZ = 0;
double targetYaw = 0;
float joyDir = 0;
const int pathNum = 343;
const int groupNum = 7;
float gridVoxelSize = 0.02;
float searchRadius = 0.45;
float gridVoxelOffsetX = 3.2;
float gridVoxelOffsetY = 4.5;
const int gridVoxelNumX = 161;
const int gridVoxelNumY = 451;
const int gridVoxelNum = gridVoxelNumX * gridVoxelNumY;
int pathList[pathNum] = {0};
float endDirPathList[pathNum] = {0};
int clearPathList[36 * pathNum] = {0};
float pathPenaltyList[36 * pathNum] = {0};
float clearPathPerGroupScore[36 * groupNum] = {0};
std::vector<int> correspondences[gridVoxelNum];
bool newLaserCloud = false;
bool newTerrainCloud = false;
double odomTime = 0;
float vehicleRoll = 0, vehiclePitch = 0, vehicleYaw = 0;
float vehicleX = 0, vehicleY = 0, vehicleZ = 0;
bool init = false;
double start_time, end_time;

//定位丢失标记
bool location_failed = false;

pcl::PointCloud<pcl::PointXYZI>::Ptr cloudKeyPoses3D(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr pointview(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloud(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloudCrop(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloudDwz(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloud(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloudCrop(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloudDwz(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloudStack[laserCloudStackNum];
pcl::PointCloud<pcl::PointXYZI>::Ptr plannerCloud(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr plannerCloudCrop(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr boundaryCloud(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr addedObstacles(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZ>::Ptr startPaths[groupNum];
#if PLOTPATHSET == 1
pcl::PointCloud<pcl::PointXYZI>::Ptr paths[pathNum];
pcl::PointCloud<pcl::PointXYZI>::Ptr freePaths(new pcl::PointCloud<pcl::PointXYZI>());
#endif
pcl::VoxelGrid<pcl::PointXYZI> laserDwzFilter, terrainDwzFilter;

void odometryHandler(const nav_msgs::Odometry::ConstPtr& odom)//载体当前姿态信息回调函数
{
  odomTime = odom->header.stamp.toSec();
  //计算姿态角
  double roll, pitch, yaw;
  geometry_msgs::Quaternion geoQuat = odom->pose.pose.orientation;
  tf::Matrix3x3(tf::Quaternion(geoQuat.x, geoQuat.y, geoQuat.z, geoQuat.w)).getRPY(roll, pitch, yaw);
  vehicleRoll = roll;
  vehiclePitch = pitch;
  vehicleYaw = yaw;
  vehicleX = odom->pose.pose.position.x - cos(yaw) * sensorOffsetX + sin(yaw) * sensorOffsetY;
  vehicleY = odom->pose.pose.position.y - sin(yaw) * sensorOffsetX - cos(yaw) * sensorOffsetY;
  vehicleZ = odom->pose.pose.position.z;
}

void laserCloudHandler(const sensor_msgs::PointCloud2ConstPtr& laserCloud2)//不使用可通行区域检测时的点云回调函数
{
  if (!useTerrainAnalysis)
  {
    //不采用可通行区域检测
    laserCloud->clear();
    pcl::fromROSMsg(*laserCloud2, *laserCloud);
    pcl::PointXYZI point;
    laserCloudCrop->clear();
    int laserCloudSize = laserCloud->points.size();
    for (int i = 0; i < laserCloudSize; i++)
    {
      point = laserCloud->points[i];
      float pointX = point.x;
      float pointY = point.y;
      float pointZ = point.z;
      //筛选一定范围内的点云
      float dis = sqrt((pointX - vehicleX) * (pointX - vehicleX) + (pointY - vehicleY) * (pointY - vehicleY));
      if (dis < adjacentRange)
      {
        point.x = pointX;
        point.y = pointY;
        point.z = pointZ;
        laserCloudCrop->push_back(point);
      }
    }
    //点云滤波
    laserCloudDwz->clear();
    laserDwzFilter.setInputCloud(laserCloudCrop);
    laserDwzFilter.filter(*laserCloudDwz);
    //新点云接受完成
    newLaserCloud = true;
  }
}

void terrainCloudHandler(const sensor_msgs::PointCloud2ConstPtr& terrainCloud2)//可通行区域点云回调函数
{
  if (useTerrainAnalysis) 
  {
    //采用可通行区域检测
    terrainCloud->clear();
    pcl::fromROSMsg(*terrainCloud2, *terrainCloud);
    pcl::PointXYZI point;
    terrainCloudCrop->clear();
    int terrainCloudSize = terrainCloud->points.size();
    for (int i = 0; i < terrainCloudSize; i++) 
    {
      point = terrainCloud->points[i];
      float pointX = point.x;
      float pointY = point.y;
      float pointZ = point.z;
      //筛选一定范围内的点云
      float dis = sqrt((pointX - vehicleX) * (pointX - vehicleX) + (pointY - vehicleY) * (pointY - vehicleY));
      if (dis < adjacentRange && (point.intensity > obstacleHeightThre || useCost))
      {
        point.x = pointX;
        point.y = pointY;
        point.z = pointZ;
        terrainCloudCrop->push_back(point);
      }
    }
    //点云滤波
    terrainCloudDwz->clear();
    terrainDwzFilter.setInputCloud(terrainCloudCrop);
    terrainDwzFilter.filter(*terrainCloudDwz);
    //新点云接受完成
    newTerrainCloud = true;
  }
}

void goalHandler(const geometry_msgs::PoseStamped::ConstPtr& goal)//局部目标点回调函数
{
  goalX = goal->pose.position.x;
  goalY = goal->pose.position.y;
  goalZ = goal->pose.position.z;
  adjustmode.data = false;
  if (init)
  {
    init = false;
  }

  
}

void targetHandler(const geometry_msgs::PoseStamped::ConstPtr& target)//导航目标点及导航参数回调函数
{
  //nav_start = target->nav_mode;
  nav_start = 1;
  //id = target->point_id;
  targetX = target->pose.position.x;
  targetY = target->pose.position.y;
  //targetZ = target->pose.position.z;
	tf::Quaternion quat;
  tf::quaternionMsgToTF(target->pose.orientation, quat);
  double roll, pitch, yaw;//定义存储r\p\y的容器
  tf::Matrix3x3(quat).getRPY(roll, pitch, yaw);//进行转换
  targetYaw = yaw;
  std::cout << "handpoint x:[" << targetX << "],y:["  << targetY << "],yaw:["  << targetYaw << "]" << std::endl;
  //info = target->point_info;
  //obs_mode = target->obsmode;
   //普通速、低速、高速切换
  /*
  if (target->speed == 0)
  maxSpeed = 0.8;
  else if (target->speed == 1)
  maxSpeed = 0.5;
  else if (target->speed == 2)
  maxSpeed = 1.0;
  */
  maxSpeed = 0.8;
  //前进后退切换
  //if (target->manner == 0)
  twoWayDrive = false;
  //else if (target->manner == 1)
  //twoWayDrive = true;
  init = true;
  adjustmode.data = false;
  arrive_inf.data = 0;
}

void webtargetHandler(const geometry_msgs::PoseStamped::ConstPtr& target)//导航目标点及导航参数回调函数
{
  //nav_start = target->nav_mode;
  nav_start = 1;
  //id = target->point_id;
  targetX = target->pose.position.x;
  targetY = target->pose.position.y;
  //targetZ = target->pose.position.z;
	// tf::Quaternion quat;
  // tf::quaternionMsgToTF(target->pose.orientation, quat);
  // double roll, pitch, yaw;//定义存储r\p\y的容器
  // tf::Matrix3x3(quat).getRPY(roll, pitch, yaw);//进行转换
  targetYaw = 0; // 是否需要改成： targetYaw = target->pose.orientation.z * M_PI / 180.0;
 // targetYaw = target->pose.orientation.z * M_PI / 180.0;
  //info = target->point_info;
  //obs_mode = target->obsmode;
   //普通速、低速、高速切换
  /*
  if (target->speed == 0)
  maxSpeed = 0.8;
  else if (target->speed == 1)
  maxSpeed = 0.5;
  else if (target->speed == 2)
  maxSpeed = 1.0;
  */
  maxSpeed = 0.8;
  //前进后退切换
  //if (target->manner == 0)
  twoWayDrive = false;
  //else if (target->manner == 1)
  //twoWayDrive = true;
  init = true;
  adjustmode.data = false;
  arrive_inf.data = 0;

  //目标点航向，deg
  // info = (target->pose.orientation.z < 360.0);

  cout << "web target:" << targetX << "," << targetY << "," << target->pose.orientation.z << endl;
}

void boundaryHandler(const geometry_msgs::PolygonStamped::ConstPtr& boundary)//导航边界回调函数，可用作检修区域
{
  //初始化
  boundaryCloud->clear();
  pcl::PointXYZI point, point1, point2;
  int boundarySize = boundary->polygon.points.size();
  //若多边形不为空，赋值第一个点
  if (boundarySize >= 1)
  {
    point2.x = boundary->polygon.points[0].x;
    point2.y = boundary->polygon.points[0].y;
    point2.z = boundary->polygon.points[0].z;
  }
  //根据多边形定点数进行循环
  for (int i = 0; i < boundarySize; i++)
  {
    point1 = point2;
    //赋值下一个点
    point2.x = boundary->polygon.points[i].x;
    point2.y = boundary->polygon.points[i].y;
    point2.z = boundary->polygon.points[i].z;
    //若两个点z方向相同，则计算其距离
    if (point1.z == point2.z)
    {
      float disX = point1.x - point2.x;
      float disY = point1.y - point2.y;
      float dis = sqrt(disX * disX + disY * disY);
      //将距离栅格化，并生成障碍物点
      int pointNum = int(dis / terrainVoxelSize) + 1;
      for (int pointID = 0; pointID < pointNum; pointID++)
      {
        point.x = float(pointID) / float(pointNum) * point1.x + (1.0 - float(pointID) / float(pointNum)) * point2.x;
        point.y = float(pointID) / float(pointNum) * point1.y + (1.0 - float(pointID) / float(pointNum)) * point2.y;
        point.z = 0;
        point.intensity = 100.0;
        //将生成的障碍物点压入边界点云中
        for (int j = 0; j < pointPerPathThre; j++)
        {
          boundaryCloud->push_back(point);
        }
      }
    }
  }
}

void addedObstaclesHandler(const sensor_msgs::PointCloud2ConstPtr& addedObstacles2)//增加障碍物回调函数
{
  addedObstacles->clear();
  pcl::fromROSMsg(*addedObstacles2, *addedObstacles);
  //将接收到的点全部作为障碍物点云
  int addedObstaclesSize = addedObstacles->points.size();
  for (int i = 0; i < addedObstaclesSize; i++)
  {
    addedObstacles->points[i].intensity = 200.0;
  }
}

void locationFailedHandler(const std_msgs::Bool::ConstPtr& msg)
{
  // 处理接收到的bool消息
    if (msg->data)
    {
      //  ROS_INFO("Received true");
    }
    else
    {
      //定位丢失， 发布istop消息
      location_failed = true;
      
    }
}

int readPlyHeader(FILE *filePtr)
{
  char str[50];
  int val, pointNum;
  string strCur, strLast;
  while (strCur != "end_header") 
  {
    val = fscanf(filePtr, "%s", str);
    if (val != 1)
    {
      printf ("\nError reading input files, exit.\n\n");
      exit(1);
    }
    strLast = strCur;
    strCur = string(str);
    if (strCur == "vertex" && strLast == "element")
    {
      val = fscanf(filePtr, "%d", &pointNum);
      if (val != 1)
      {
        printf ("\nError reading input files, exit.\n\n");
        exit(1);
      }
    }
  }
  return pointNum;
}

void readStartPaths()
{
  string fileName = pathFolder + "/startPaths.ply";
  FILE *filePtr = fopen(fileName.c_str(), "r");
  if (filePtr == NULL)
  {
    printf ("\nCannot read input files, exit.\n\n");
    exit(1);
  }
  int pointNum = readPlyHeader(filePtr);
  pcl::PointXYZ point;
  int val1, val2, val3, val4, groupID;
  for (int i = 0; i < pointNum; i++)
  {
    val1 = fscanf(filePtr, "%f", &point.x);
    val2 = fscanf(filePtr, "%f", &point.y);
    val3 = fscanf(filePtr, "%f", &point.z);
    val4 = fscanf(filePtr, "%d", &groupID);
    if (val1 != 1 || val2 != 1 || val3 != 1 || val4 != 1)
    {
      printf ("\nError reading input files, exit.\n\n");
        exit(1);
    }
    if (groupID >= 0 && groupID < groupNum)
    {
      startPaths[groupID]->push_back(point);
    }
  }
  fclose(filePtr);
}

#if PLOTPATHSET == 1
void readPaths()
{
  string fileName = pathFolder + "/paths.ply";
  FILE *filePtr = fopen(fileName.c_str(), "r");
  if (filePtr == NULL)
  {
    printf ("\nCannot read input files, exit.\n\n");
    exit(1);
  }
  int pointNum = readPlyHeader(filePtr);
  pcl::PointXYZI point;
  int pointSkipNum = 30;
  int pointSkipCount = 0;
  int val1, val2, val3, val4, val5, pathID;
  for (int i = 0; i < pointNum; i++)
  {
    val1 = fscanf(filePtr, "%f", &point.x);
    val2 = fscanf(filePtr, "%f", &point.y);
    val3 = fscanf(filePtr, "%f", &point.z);
    val4 = fscanf(filePtr, "%d", &pathID);
    val5 = fscanf(filePtr, "%f", &point.intensity);
    if (val1 != 1 || val2 != 1 || val3 != 1 || val4 != 1 || val5 != 1)
    {
      printf ("\nError reading input files, exit.\n\n");
        exit(1);
    }
    if (pathID >= 0 && pathID < pathNum)
    {
      pointSkipCount++;
      if (pointSkipCount > pointSkipNum)
      {
        paths[pathID]->push_back(point);
        pointSkipCount = 0;
      }
    }
  }
  fclose(filePtr);
}
#endif


void readPathList()
{
  string fileName = pathFolder + "/pathList.ply";
  FILE *filePtr = fopen(fileName.c_str(), "r");
  if (filePtr == NULL)
  {
    printf ("\nCannot read input files, exit.\n\n");
    exit(1);
  }
  if (pathNum != readPlyHeader(filePtr))
  {
    printf ("\nIncorrect path number, exit.\n\n");
    exit(1);
  }
  int val1, val2, val3, val4, val5, pathID, groupID;
  float endX, endY, endZ;
  for (int i = 0; i < pathNum; i++)
  {
    val1 = fscanf(filePtr, "%f", &endX);
    val2 = fscanf(filePtr, "%f", &endY);
    val3 = fscanf(filePtr, "%f", &endZ);
    val4 = fscanf(filePtr, "%d", &pathID);
    val5 = fscanf(filePtr, "%d", &groupID);
    if (val1 != 1 || val2 != 1 || val3 != 1 || val4 != 1 || val5 != 1)
    {
      printf ("\nError reading input files, exit.\n\n");
        exit(1);
    }
    if (pathID >= 0 && pathID < pathNum && groupID >= 0 && groupID < groupNum)
    {
      pathList[pathID] = groupID;
      endDirPathList[pathID] = 2.0 * atan2(endY, endX) * 180 / PI;
    }
  }
  fclose(filePtr);
}

void readCorrespondences()
{
  string fileName = pathFolder + "/correspondences.txt";
  FILE *filePtr = fopen(fileName.c_str(), "r");
  if (filePtr == NULL)
  {
    printf ("\nCannot read input files, exit.\n\n");
    exit(1);
  }
  int val1, gridVoxelID, pathID;
  for (int i = 0; i < gridVoxelNum; i++)
  {
    val1 = fscanf(filePtr, "%d", &gridVoxelID);
    if (val1 != 1)
    {
      printf ("\nError reading input files, exit.\n\n");
        exit(1);
    }
    while (1)
    {
      val1 = fscanf(filePtr, "%d", &pathID);
      if (val1 != 1)
      {
        printf ("\nError reading input files, exit.\n\n");
          exit(1);
      }
      if (pathID != -1)
      {
        if (gridVoxelID >= 0 && gridVoxelID < gridVoxelNum && pathID >= 0 && pathID < pathNum)
        {
          correspondences[gridVoxelID].push_back(pathID);
        }
      } 
      else
      {
        break;
      }
    }
  }

  fclose(filePtr);
}

void calibrationHandler(const std_msgs::Int8::ConstPtr& calibration)//精调回调函数
{
    info = calibration->data;//1为使用精调，0为不使用精调
}

int main(int argc, char** argv)
{
  ros::init(argc, argv, "localPlanner");
  ros::NodeHandle nh;
  ros::NodeHandle private_nh("~");
  private_nh.param("pathFolder", pathFolder, std::string("/home/<USER>/NR_Navigation/src/local_planner"));
  private_nh.param("vehicleLength", vehicleLength, 1.2);
  private_nh.param("vehicleWidth", vehicleWidth, 0.8);
  private_nh.param("sensorOffsetX", sensorOffsetX, 0.0);
  private_nh.param("sensorOffsetY", sensorOffsetY, 0.0);
  private_nh.param("laserVoxelSize", laserVoxelSize, 0.05);
  private_nh.param("terrainVoxelSize", terrainVoxelSize, 0.2);
  private_nh.param("useTerrainAnalysis", useTerrainAnalysis, true);
  private_nh.param("checkRotObstacle", checkRotObstacle, false);
  private_nh.param("adjacentRange", adjacentRange, 4.25);
  private_nh.param("obstacleHeightThre", obstacleHeightThre, 0.15);
  private_nh.param("groundHeightThre", groundHeightThre, 0.1);
  private_nh.param("costHeightThre", costHeightThre, 0.1);
  private_nh.param("costScore", costScore, 0.02);
  private_nh.param("useCost", useCost, false);
  private_nh.param("pointPerPathThre", pointPerPathThre, 2);
  private_nh.param("minRelZ", minRelZ, -0.8);
  private_nh.param("maxRelZ", maxRelZ, 0.25);
  private_nh.param("dirWeight", dirWeight, 0.02);
  private_nh.param("dirThre", dirThre, 90.0);
  private_nh.param("dirToVehicle", dirToVehicle, false);
  private_nh.param("pathScale", pathScale, 1.25);
  private_nh.param("minPathScale", minPathScale, 0.75);
  private_nh.param("pathScaleStep", pathScaleStep, 0.25);
  private_nh.param("pathScaleBySpeed", pathScaleBySpeed, true);
  private_nh.param("minPathRange", minPathRange, 1.0);
  private_nh.param("pathRangeStep", pathRangeStep, 0.5);
  private_nh.param("pathRangeBySpeed", pathRangeBySpeed, true);
  private_nh.param("pathCropByGoal", pathCropByGoal, true);
  private_nh.param("goalClearRange", goalClearRange, 0.5);
  private_nh.param("arrived_dis_threshold", arrived_dis_threshold, 0.2);
  ROS_INFO("arrived_dis_threshold: %f", arrived_dis_threshold);
  //订阅载体当前位姿信息
  ros::Subscriber subOdometry = nh.subscribe<nav_msgs::Odometry> ("/state_estimation", 1, odometryHandler);
  //订阅未进行可通行区域检测的点云数据
  ros::Subscriber subLaserCloud = nh.subscribe<sensor_msgs::PointCloud2> ("/registered_scan", 5, laserCloudHandler);
  //订阅可通行区域点云数据
  ros::Subscriber subTerrainCloud = nh.subscribe<sensor_msgs::PointCloud2> ("/terrain_map", 5, terrainCloudHandler);
  //订阅局部目标点信息
  ros::Subscriber subGoal = nh.subscribe<geometry_msgs::PoseStamped> ("/local_goal", 1, goalHandler);
  //订阅导航目标点及导航参数信息
  ros::Subscriber subTarget = nh.subscribe<geometry_msgs::PoseStamped> ("/move_base_simple/goal", 1, targetHandler);
  //订阅导航边界信息
   //订阅导航目标点及导航参数信息
  ros::Subscriber subwebTarget = nh.subscribe<geometry_msgs::PoseStamped> ("/web_goal_pose", 1, webtargetHandler);
  
  ros::Subscriber subBoundary = nh.subscribe<geometry_msgs::PolygonStamped> ("/navigation_boundary", 5, boundaryHandler);
  //订阅新增障碍物信息
  ros::Subscriber subAddedObstacles = nh.subscribe<sensor_msgs::PointCloud2> ("/added_obstacles", 5, addedObstaclesHandler);
  
  //订阅定位失败消息
  ros::Subscriber subLocal_flag = nh.subscribe<std_msgs::Bool>("/Local_flag", 1, locationFailedHandler);     /***定位丢失标志位：true为成功、false为丢失***/

  
  //发布粗调精调模式信息
  ros::Publisher pubMode = nh.advertise<std_msgs::Bool> ("/adjustmode", 5);
  //发布局部轨迹信息
  ros::Publisher pubPath = nh.advertise<nav_msgs::Path> ("/local_path", 5);
  //发布停止信息
  ros::Publisher pubStop = nh.advertise<std_msgs::Int8> ("/stop", 1 );

  ros::Publisher innerpubStop = nh.advertise<std_msgs::Int8> ("/istop", 1 );
  //发布重规划信息
  ros::Publisher pubReplan = nh.advertise<std_msgs::Int8> ("/replan", 1 );
  
  ros::Subscriber subcalibration = nh.subscribe<std_msgs::Int8> ("/calibration", 1, calibrationHandler);
  
  // ros::Publisher goal_success_pub_= private_nh.advertise<std_msgs::Int8>("/goal_success", 1);
  //发布导航状态信息
  //ros::Publisher pubResult = nh.advertise<local_planner::NavigationResult> ("/navigation_result", 1);
  nav_msgs::Path path;
  //发布未选择的局部轨迹信息
  #if PLOTPATHSET == 1
  ros::Publisher pubFreePaths = nh.advertise<sensor_msgs::PointCloud2> ("/free_paths", 2);
  #endif
  printf ("\nReading path files.\n");
  //离线轨迹及体素初始化
  for (int i = 0; i < laserCloudStackNum; i++)
  {
    laserCloudStack[i].reset(new pcl::PointCloud<pcl::PointXYZI>());
  }
  for (int i = 0; i < groupNum; i++)
  {
    startPaths[i].reset(new pcl::PointCloud<pcl::PointXYZ>());
  }
  #if PLOTPATHSET == 1
  for (int i = 0; i < pathNum; i++)
  {
    paths[i].reset(new pcl::PointCloud<pcl::PointXYZI>());
  }
  #endif
  for (int i = 0; i < gridVoxelNum; i++)
  {
    correspondences[i].resize(0);
  }
  //点云滤波器初始化
  laserDwzFilter.setLeafSize(laserVoxelSize, laserVoxelSize, laserVoxelSize);
  terrainDwzFilter.setLeafSize(terrainVoxelSize, terrainVoxelSize, terrainVoxelSize);
  //读取离线轨迹及体素
  readStartPaths();
  #if PLOTPATHSET == 1
  readPaths();
  #endif
  readPathList();
  readCorrespondences();
  //adjustmode.data = true;
  printf ("\nInitialization complete.\n\n");
  //初始化完成
  ros::Rate rate(100);
  bool status = ros::ok();
  while (status)
  {
    ros::spinOnce();

    if(location_failed)
    {
   //   std_msgs::Int8 safetystop;
   //   safetystop.data = 1;     
   //   innerpubStop.publish(safetystop);
   //   ROS_INFO("定位丢失后, 发布istop消息");
   //   location_failed = false;
    }

    if (newLaserCloud || newTerrainCloud)
    {
      if (newLaserCloud)//不采用可通行区域检测
      {
        //将点云栅格化存入规划点云中
        newLaserCloud = false;
        laserCloudStack[laserCloudCount]->clear();
        *laserCloudStack[laserCloudCount] = *laserCloudDwz;
        laserCloudCount = (laserCloudCount + 1) % laserCloudStackNum;
        plannerCloud->clear();
        for (int i = 0; i < laserCloudStackNum; i++)
        {
          *plannerCloud += *laserCloudStack[i];
        }
      }
      if (newTerrainCloud)//采用可通行区域检测
      {
        //将可通行区域点云存入规划点云中
        newTerrainCloud = false;
        plannerCloud->clear();
        *plannerCloud = *terrainCloudDwz;
      }
      //方便后续程序使用
      float sinVehicleRoll = sin(vehicleRoll);
      float cosVehicleRoll = cos(vehicleRoll);
      float sinVehiclePitch = sin(vehiclePitch);
      float cosVehiclePitch = cos(vehiclePitch);
      float sinVehicleYaw = sin(vehicleYaw);
      float cosVehicleYaw = cos(vehicleYaw);
      //将规划点云转到载体系下
      pcl::PointXYZI point;
      plannerCloudCrop->clear();
      int plannerCloudSize = plannerCloud->points.size();
      for (int i = 0; i < plannerCloudSize; i++)
      {
        float pointX1 = plannerCloud->points[i].x - vehicleX;
        float pointY1 = plannerCloud->points[i].y - vehicleY;
        float pointZ1 = plannerCloud->points[i].z - vehicleZ;
        point.x = pointX1 * cosVehicleYaw + pointY1 * sinVehicleYaw;
        point.y = -pointX1 * sinVehicleYaw + pointY1 * cosVehicleYaw;
        point.z = pointZ1;
        point.intensity = plannerCloud->points[i].intensity;
        //若不使用可通行区域检测需对点云范围进行筛选，并存入规划点云中
        float dis = sqrt(point.x * point.x + point.y * point.y);
        if (dis < adjacentRange && ((point.z > minRelZ && point.z < maxRelZ) || useTerrainAnalysis)) plannerCloudCrop->push_back(point);
      }
      //将导航边界点云转到载体系下
      int boundaryCloudSize = boundaryCloud->points.size();
      for (int i = 0; i < boundaryCloudSize; i++)
      {
        point.x = ((boundaryCloud->points[i].x - vehicleX) * cosVehicleYaw + (boundaryCloud->points[i].y - vehicleY) * sinVehicleYaw);
        point.y = (-(boundaryCloud->points[i].x - vehicleX) * sinVehicleYaw + (boundaryCloud->points[i].y - vehicleY) * cosVehicleYaw);
        point.z = boundaryCloud->points[i].z;
        point.intensity = boundaryCloud->points[i].intensity;
        //对导航边界点云范围进行筛选，并存入规划点云中
        float dis = sqrt(point.x * point.x + point.y * point.y);
        if (dis < adjacentRange) plannerCloudCrop->push_back(point);
      }
      //将新增障碍物点云转到载体系下
      int addedObstaclesSize = addedObstacles->points.size();
      for (int i = 0; i < addedObstaclesSize; i++)
      {
        point.x = ((addedObstacles->points[i].x - vehicleX) * cosVehicleYaw + (addedObstacles->points[i].y - vehicleY) * sinVehicleYaw);
        point.y = (-(addedObstacles->points[i].x - vehicleX) * sinVehicleYaw + (addedObstacles->points[i].y - vehicleY) * cosVehicleYaw);
        point.z = addedObstacles->points[i].z;
        point.intensity = addedObstacles->points[i].intensity;
        //对新增障碍物点云范围进行筛选，并存入规划点云中
        float dis = sqrt(point.x * point.x + point.y * point.y);
        if (dis < adjacentRange) plannerCloudCrop->push_back(point);
      }
      //根据速度设置轨迹范围
      float pathRange = adjacentRange;
      if (pathRangeBySpeed) pathRange = adjacentRange * maxSpeed;
      if (pathRange < minPathRange) pathRange = minPathRange;
      float relativeGoalDis = adjacentRange;
      //进入避障模式导航
      if (!adjustmode.data && nav_start == 1 && !init)
      {
        //根据当前载体位置与终点位置判断进入的导航模式
        double distance = sqrt((targetX - vehicleX) * (targetX - vehicleX) + (targetY - vehicleY) * (targetY - vehicleY));
        // std::cout<< "distance:" << distance << std::endl;
        std_msgs::Int8 safetystop;
        //导航模式    
        //不进入精调  
        adjustmode.data = false;
        safetystop.data = 0;
        if(arrive_inf.data == 0)
        {
          if(distance < arrived_dis_threshold)//进入精调模式距离
          {
            if(info == 1)//任务点需切精调
            {
              adjustmode.data = true;
              safetystop.data = 0;
              std::cout << "进入精调模式" << std::endl;
              std::cout << "target:" << targetX << "," << targetY << "," << targetYaw<<std::endl;
            }
            else if(info == 0)//任务点不需切精调
            {
            	//停止标志位
            	adjustmode.data = false;
            	safetystop.data = 1;  
            	nav_start = 0;
            	std::cout << "到达终点" << std::endl;
            	std::cout << "target:" << targetX << "," << targetY << "," << targetYaw<<std::endl;
            	std::cout << "vehicle:" << vehicleX << "," << vehicleY << "," << vehicleYaw <<std::endl;

            	pubStop.publish(safetystop);//发布停止标志位
            	innerpubStop.publish(safetystop);//发布innerstop停止标志位
            	arrive_inf.data = 1;//保证仅发布一次到点信息。直到新目标点进入回调前不再进入此判断
            }
            
          }
        }
        innerpubStop.publish(safetystop);
        //发布导航模式和停止信息
        pubMode.publish(adjustmode);
        // pubStop.publish(safetystop);
        //将局部目标点转到载体系下并计算距离的夹角
        float relativeGoalX = ((goalX - vehicleX) * cosVehicleYaw + (goalY - vehicleY) * sinVehicleYaw);
        float relativeGoalY = (-(goalX - vehicleX) * sinVehicleYaw + (goalY - vehicleY) * cosVehicleYaw);
        relativeGoalDis = sqrt(relativeGoalX * relativeGoalX + relativeGoalY * relativeGoalY);
        joyDir = atan2(relativeGoalY, relativeGoalX) * 180 / PI;
        //若不是双向驱动则把夹角转到-90度到90度内
        if (!twoWayDrive)
        {
          if (joyDir > 90.0) joyDir = 90.0;
          else if (joyDir < -90.0) joyDir = -90.0;
        }
        //根据速度设置轨迹尺度，速度越快尺度越大
        bool pathFound = false;
        float defPathScale = pathScale;
        if (pathScaleBySpeed) pathScale = defPathScale * maxSpeed;
        if (pathScale < minPathScale) pathScale = minPathScale;
        //进入规划主循环，路径范围和尺度会不断变小直到找到路径或者小于最小阈值
        while (pathScale >= minPathScale && pathRange >= minPathRange)
        {
          //轨迹列表和得分初始化
          for (int i = 0; i < 36 * pathNum; i++)
          {
            clearPathList[i] = 0;
            pathPenaltyList[i] = 0;
          }
          for (int i = 0; i < 36 * groupNum; i++)
          {
            clearPathPerGroupScore[i] = 0;
          }
          //根据载体长宽生成碰撞模型
          float minObsAngCW = -180.0;
          float minObsAngCCW = 180.0;
          float diameter = sqrt(vehicleLength / 2.0 * vehicleLength / 2.0 + vehicleWidth / 2.0 * vehicleWidth / 2.0);
          float angOffset = atan2(vehicleWidth, vehicleLength) * 180.0 / PI;
          //利用轨迹尺度对障碍物点云进行处理
          int plannerCloudCropSize = plannerCloudCrop->points.size();
          for (int i = 0; i < plannerCloudCropSize; i++)
          {
            float x = plannerCloudCrop->points[i].x / pathScale;
            float y = plannerCloudCrop->points[i].y / pathScale;
            float h = plannerCloudCrop->points[i].intensity;
            float dis = sqrt(x * x + y * y);
            //距离小于定义的路径范围被认为是无用的
            if (dis < pathRange / pathScale && (dis <= (relativeGoalDis + goalClearRange) / pathScale || !pathCropByGoal))
            {
              for (int rotDir = 0; rotDir < 36; rotDir++)
              {
                //将车体一周分成36等分，10度为一份计算路径
                float rotAng = (10.0 * rotDir - 180.0) * PI / 180;//路径距车体朝向的夹角
                float angDiff = fabs(joyDir - (10.0 * rotDir - 180.0));//路径距目标点间的夹角
                if (angDiff > 180.0) angDiff = 360.0 - angDiff;
                //目标点角度差值大于车体转向角度的阈值且以目标点为方向，计算目标点方向左右的路径
                //车体朝向角度差大于车体转向角度的阈值且当前朝向绝对值小于90度即向前且以车辆朝向为方向，计算车辆朝向左右的路径
                //车体朝向角度差大于车体转向角度的阈值且当前朝向绝对值大于90度即向后且以车辆朝向为方向，计算车辆朝向左右的路径
                if ((angDiff > dirThre && !dirToVehicle) || (fabs(10.0 * rotDir - 180.0) > dirThre && fabs(joyDir) <= 90.0 && dirToVehicle) ||
                    ((10.0 * rotDir > dirThre && 360.0 - 10.0 * rotDir > dirThre) && fabs(joyDir) > 90.0 && dirToVehicle)) continue;
                //将障碍物转到以该轨迹方向为坐标轴的坐标系下
                float x2 = cos(rotAng) * x + sin(rotAng) * y;
                float y2 = -sin(rotAng) * x + cos(rotAng) * y;
                //Y方向上的尺度变换和生成体素网格时保持一致
                float scaleY = x2 / gridVoxelOffsetX + searchRadius / gridVoxelOffsetY * (gridVoxelOffsetX - x2) / gridVoxelOffsetX;
                //计算体素网格的索引
                int indX = int((gridVoxelOffsetX + gridVoxelSize / 2 - x2) / gridVoxelSize);
                int indY = int((gridVoxelOffsetY + gridVoxelSize / 2 - y2 / scaleY) / gridVoxelSize);
                if (indX >= 0 && indX < gridVoxelNumX && indY >= 0 && indY < gridVoxelNumY)//确保不会越界
                {
                  int ind = gridVoxelNumY * indX + indY;//得到索引序号
                  int blockedPathByVoxelNum = correspondences[ind].size();//当前序号的体素网格,占据了多少条路径
                  for (int j = 0; j < blockedPathByVoxelNum; j++)
                  {
                    //当前激光点的高度大于obstacleHeightThre阈值,或者未使用地面分割时,累加
                    if (h > obstacleHeightThre || !useTerrainAnalysis)
                      clearPathList[pathNum * rotDir + correspondences[ind][j]]++;
                    else
                    {
                      //在使用了地面分割且激光点分割后高度小于障碍物高度阈值，并且当前高度大于原有值,且大于地面高度阈值
                      if (pathPenaltyList[pathNum * rotDir + correspondences[ind][j]] < h && h > groundHeightThre)
                        pathPenaltyList[pathNum * rotDir + correspondences[ind][j]] = h;
                    }
                  }
                }
              }
            }
            //点云距离小于车辆半径，但点云不在车体内部，并且超过了障碍物高度阈值
            if (dis < diameter / pathScale && (fabs(x) > vehicleLength / pathScale / 2.0 || fabs(y) > vehicleWidth / pathScale / 2.0) &&
                (h > obstacleHeightThre || !useTerrainAnalysis) && checkRotObstacle)
            {
              //点云与车体朝向的夹角
              float angObs = atan2(y, x) * 180.0 / PI;
              if (angObs > 0)//左边
              {
                if (minObsAngCCW > angObs - angOffset) minObsAngCCW = angObs - angOffset;
                if (minObsAngCW < angObs + angOffset - 180.0) minObsAngCW = angObs + angOffset - 180.0;
              }
              else//右边
              {
                if (minObsAngCW < angObs + angOffset) minObsAngCW = angObs + angOffset;
                if (minObsAngCCW > 180.0 + angObs - angOffset) minObsAngCCW = 180.0 + angObs - angOffset;
              }
            }
          }
          //对顺、逆时针最小障碍物夹角进行限幅
          if (minObsAngCW > 0) minObsAngCW = 0;
          if (minObsAngCCW < 0) minObsAngCCW = 0;

          for (int i = 0; i < 36 * pathNum; i++)
          {
            int rotDir = int(i / pathNum);
            float angDiff = fabs(joyDir - (10.0 * rotDir - 180.0));//路径距目标点间的夹角
            if (angDiff > 180.0) angDiff = 360.0 - angDiff;
            //目标点角度差值大于车体转向角度的阈值且以目标点为方向，计算目标点方向左右的路径
            //车体朝向角度差大于车体转向角度的阈值且当前朝向绝对值小于90度即向前且以车辆朝向为方向，计算车辆朝向左右的路径
            //车体朝向角度差大于车体转向角度的阈值且当前朝向绝对值大于90度即向后且以车辆朝向为方向，计算车辆朝向左右的路径
            if ((angDiff > dirThre && !dirToVehicle) || (fabs(10.0 * rotDir - 180.0) > dirThre && fabs(joyDir) <= 90.0 && dirToVehicle) ||
                ((10.0 * rotDir > dirThre && 360.0 - 10.0 * rotDir > dirThre) && fabs(joyDir) > 90.0 && dirToVehicle)) continue;
            //当一条路径上存在小于两个障碍点
            if (clearPathList[i] < pointPerPathThre)
            {
              //计算障碍物得分,pathPenaltyList越高，惩罚得分penaltyScore也就越低
              float penaltyScore = 1.0 - pathPenaltyList[i] / costHeightThre;
              if (penaltyScore < costScore) penaltyScore = costScore;
              //endDirPathList代表了该条路径末端点与当前位置的角度，dirDiff则是该条路径与目标点之间的角度差值，会用于计算路径的得分
              float dirDiff = fabs(joyDir - endDirPathList[i % pathNum] - (10.0 * rotDir - 180.0));
              if (dirDiff > 360.0) dirDiff -= 360.0;
              if (dirDiff > 180.0) dirDiff = 360.0 - dirDiff;
              //rotDirW代表了该条路径的方向与当前车辆朝向的角度差，也会被用于路径得分的计算,前后高左右低
              float rotDirW;
              if (rotDir < 18) rotDirW = fabs(fabs(rotDir - 9) + 1);
              else rotDirW = fabs(fabs(rotDir - 27) + 1);
              //计算路径得分
              float score = (1 - sqrt(sqrt(dirWeight * dirDiff))) * rotDirW * rotDirW * rotDirW * rotDirW * penaltyScore;
              if (score > 0) clearPathPerGroupScore[groupNum * rotDir + pathList[i % pathNum]] += score;
            }
          }
          //遍历7*36条路径，选择最优路径
          float maxScore = 0;
          int selectedGroupID = -1;
          for (int i = 0; i < 36 * groupNum; i++)
          {
            int rotDir = int(i / groupNum);
            float rotAng = (10.0 * rotDir - 180.0) * PI / 180;
            float rotDeg = 10.0 * rotDir;
            if (rotDeg > 180.0) rotDeg -= 360.0;
            //路径得分大于最高得分且满足最小顺、逆时针障碍物角度的限制
            if (maxScore < clearPathPerGroupScore[i] && ((rotAng * 180.0 / PI > minObsAngCW && rotAng * 180.0 / PI < minObsAngCCW) ||
                (rotDeg > minObsAngCW && rotDeg < minObsAngCCW && twoWayDrive) || !checkRotObstacle))
            {
              maxScore = clearPathPerGroupScore[i];
              selectedGroupID = i;
            }
          }
          //存在满足条件的路径
          if (selectedGroupID >= 0)
          {
            int rotDir = int(selectedGroupID / groupNum);
            float rotAng = (10.0 * rotDir - 180.0) * PI / 180;
            selectedGroupID = selectedGroupID % groupNum;
            //计算满足条件的路径长度
            int selectedPathLength = startPaths[selectedGroupID]->points.size();
            path.poses.resize(selectedPathLength);
            for (int i = 0; i < selectedPathLength; i++)
            {
              float x = startPaths[selectedGroupID]->points[i].x;
              float y = startPaths[selectedGroupID]->points[i].y;
              float z = startPaths[selectedGroupID]->points[i].z;
              float dis = sqrt(x * x + y * y);
              //将满足条件的路径在路径范围和目标点距离内的一段赋给局部路径
              if (dis <= pathRange / pathScale && dis <= relativeGoalDis / pathScale)
              {
                path.poses[i].pose.position.x = pathScale * (cos(rotAng) * x - sin(rotAng) * y);
                path.poses[i].pose.position.y = pathScale * (sin(rotAng) * x + cos(rotAng) * y);
                path.poses[i].pose.position.z = pathScale * z;
              } 
              else
              {
                //超出范围了重新更改局部路径的大小
                path.poses.resize(i);
                break;
              }
            }
            //发布局部路径信息
            path.header.stamp = ros::Time().fromSec(odomTime);
            path.header.frame_id = "vehicle";
            pubPath.publish(path);
            //获得可选路径信息
            #if PLOTPATHSET == 1
            freePaths->clear();
            for (int i = 0; i < 36 * pathNum; i++)
            {
              int rotDir = int(i / pathNum);
              float rotAng = (10.0 * rotDir - 180.0) * PI / 180;
              float rotDeg = 10.0 * rotDir;
              if (rotDeg > 180.0) rotDeg -= 360.0;
              float angDiff = fabs(joyDir - (10.0 * rotDir - 180.0));
              if (angDiff > 180.0) angDiff = 360.0 - angDiff;
              //目标点角度差值大于车体转向角度的阈值且以目标点为方向，计算目标点方向左右的路径
              //车体朝向角度差大于车体转向角度的阈值且当前朝向绝对值小于90度即向前且以车辆朝向为方向，计算车辆朝向左右的路径
              //车体朝向角度差大于车体转向角度的阈值且当前朝向绝对值大于90度即向后且以车辆朝向为方向，计算车辆朝向左右的路径
              //路径方向在最小顺、逆时针障碍物角度阈值范围内
              if ((angDiff > dirThre && !dirToVehicle) || (fabs(10.0 * rotDir - 180.0) > dirThre && fabs(joyDir) <= 90.0 && dirToVehicle) ||
                  ((10.0 * rotDir > dirThre && 360.0 - 10.0 * rotDir > dirThre) && fabs(joyDir) > 90.0 && dirToVehicle) ||
                  !((rotAng * 180.0 / PI > minObsAngCW && rotAng * 180.0 / PI < minObsAngCCW) ||
                  (rotDeg > minObsAngCW && rotDeg < minObsAngCCW && twoWayDrive) || !checkRotObstacle)) continue;
              //该条路径上障碍物点小于两个
              if (clearPathList[i] < pointPerPathThre)
              {
                int freePathLength = paths[i % pathNum]->points.size();
                for (int j = 0; j < freePathLength; j++)
                {
                  point = paths[i % pathNum]->points[j];
                  float x = point.x;
                  float y = point.y;
                  float z = point.z;
                  float dis = sqrt(x * x + y * y);
                  //选择离线路径的一定范围内作为可选择路径
                  if (dis <= pathRange / pathScale && (dis <= (relativeGoalDis + goalClearRange) / pathScale || !pathCropByGoal))
                  {
                    point.x = pathScale * (cos(rotAng) * x - sin(rotAng) * y);
                    point.y = pathScale * (sin(rotAng) * x + cos(rotAng) * y);
                    point.z = pathScale * z;
                    point.intensity = 1.0;
                    freePaths->push_back(point);
                  }
                }
              }
            }
            //发布可选路径信息
            sensor_msgs::PointCloud2 freePaths2;
            pcl::toROSMsg(*freePaths, freePaths2);
            freePaths2.header.stamp = ros::Time().fromSec(odomTime);
            freePaths2.header.frame_id = "vehicle";
            pubFreePaths.publish(freePaths2);
            #endif
          }
          //若不存在满足条件的路径
          if (selectedGroupID < 0)
          {
            //缩小路径范围和路径尺度进行下一次循环
            if (pathScale >= minPathScale + pathScaleStep)
            {
              pathScale -= pathScaleStep;
              pathRange = adjacentRange * pathScale / defPathScale;
            }
            else pathRange -= pathRangeStep;
          } 
          else 
          {
            //已找到路径，退出循环
            pathFound = true;
            break;
          }
        }
        pathScale = defPathScale;
        //如果路径尺度到最小了还没有找到可走路径
        if (!pathFound)
        {
          end_time = pcl::getTime();
          if (end_time - start_time > 5)
          {
            std_msgs::Int8 replan;
            replan.data = 1;
            pubReplan.publish(replan);
            // nav_start = 0;
          }
          path.poses.resize(1);
          path.poses[0].pose.position.x = 0;
          path.poses[0].pose.position.y = 0;
          path.poses[0].pose.position.z = 0;
          //发布空的局部路径
          path.header.stamp = ros::Time().fromSec(odomTime);
          path.header.frame_id = "vehicle";
          pubPath.publish(path);
          //发布空的可选择路径
          #if PLOTPATHSET == 1
          freePaths->clear();
          sensor_msgs::PointCloud2 freePaths2;
          pcl::toROSMsg(*freePaths, freePaths2);
          freePaths2.header.stamp = ros::Time().fromSec(odomTime);
          freePaths2.header.frame_id = "vehicle";
          pubFreePaths.publish(freePaths2);
          #endif
        }
        else
        {
          start_time = pcl::getTime();
          end_time = pcl::getTime();
          std_msgs::Int8 replan;
          replan.data = 0;
          pubReplan.publish(replan);
        }
      }
      else if(adjustmode.data && nav_start == 1 && !init)
      {
        
      }
    }
    status = ros::ok();
    rate.sleep();
  }
  return 0;
}
