cmake_minimum_required(VERSION 3.0.2)
project(terrain_analysis)

add_compile_options(-std=c++17 -O3)
set(CMAKE_BUILD_TYPE "Release")

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  nav_msgs
  sensor_msgs
  pcl_ros
  pcl_conversions
  geometry_msgs
  laser_geometry
  cv_bridge
)


find_package(OpenCV REQUIRED)

catkin_package(
  INCLUDE_DIRS include
  LIBRARIES
  CATKIN_DEPENDS roscpp rospy std_msgs nav_msgs sensor_msgs pcl_ros pcl_conversions geometry_msgs laser_geometry cv_bridge
)

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
  ${PCL_INCLUDE_DIRS}
  ${OpenCV_INCLUDE_DIRS}
)

add_executable(terrain_analysis src/terrain_analysis.cpp)
target_link_libraries(terrain_analysis ${catkin_LIBRARIES} ${PCL_LIBRARIES} ${OpenCV_LIBRARIES})
