#include <dynamic_detector/dynamic_detector.hpp>

using namespace std;

std::string cloud_topic;
std::string odom_topic;
std::string terrain_cloud_topic;
double width, resolution, occupancy_threshold, log_odds_increase, log_odds_decrease, updatetime, vehicle_length, vehicle_width, z_max, z_min;
int beam_num;
ros::Subscriber sub_cloud;
ros::Subscriber sub_odom;
ros::Subscriber sub_pose;
ros::Publisher pub_terrain_cloud;
ros::Publisher pub_odom;
nav_msgs::Odometry::Ptr odometry(new nav_msgs::Odometry);
Eigen::Affine3d lidar_k2init;
DynamicCloudDetector Dynamic;
vector<nav_msgs::Odometry> odom_buffer;

void callbackCloud(const sensor_msgs::PointCloud2::ConstPtr& cloud_msg)
{
    // cout<<1111<<endl;
    sensor_msgs::PointCloud2::Ptr terrain_cloud(new sensor_msgs::PointCloud2);
    Dynamic.process(cloud_msg, odometry, terrain_cloud);
    pub_terrain_cloud.publish(*terrain_cloud);
    // double min_time = std::numeric_limits<double>::infinity();
    // int min_index = -1;
    // for (int i = 0; i < odom_buffer.size(); i++)
    // {
    //     if (odom_buffer[i].header.stamp.toNSec() -cloud_msg->header.stamp.toNSec() < min_time)
    //     {
    //         min_index = i;
    //         min_time = odom_buffer[i].header.stamp.toNSec() -cloud_msg->header.stamp.toNSec();
    //     }
    // }
    // *odometry = odom_buffer[min_index];
    // odom_buffer.erase(odom_buffer.begin(), odom_buffer.begin() + min_index + 1);
    // sensor_msgs::PointCloud2::Ptr terrain_cloud(new sensor_msgs::PointCloud2);
    // Dynamic.process(cloud_msg, odometry, terrain_cloud);
    odometry->header.frame_id = "map";
    odometry->header.stamp = ros::Time::now();
    // pub_terrain_cloud.publish(*terrain_cloud);
    pub_odom.publish(*odometry);
}

void callbackOdom(const nav_msgs::Odometry::ConstPtr& odom_msg)
{

    *odometry = *odom_msg;
    Eigen::Vector3d t;
    t << odom_msg->pose.pose.position.x, odom_msg->pose.pose.position.y, odom_msg->pose.pose.position.z;
    Eigen::Quaterniond t_Q;
    t_Q.x() = odom_msg->pose.pose.orientation.x;
    t_Q.y() = odom_msg->pose.pose.orientation.y;
    t_Q.z() = odom_msg->pose.pose.orientation.z;
    t_Q.w() = odom_msg->pose.pose.orientation.w;
    lidar_k2init = ( Eigen::Translation3d (t.cast<double>()) * Eigen::AngleAxisd ((t_Q.toRotationMatrix()).cast<double>()));  
}

void callbackPose(const geometry_msgs::PoseStamped::ConstPtr& pose)
{
    odometry->header = pose->header;
    odometry->pose.pose = pose->pose;
    odom_buffer.push_back(*odometry);
}

int main(int argc, char**argv) {

    ros::init(argc, argv, "terrain_analysis");
    ros::NodeHandle nh;
    ros::NodeHandle private_nh("~");

    private_nh.param("cloud_topic", cloud_topic, std::string("/cloud_registered"));
    private_nh.param("odom_topic", odom_topic, std::string("/Odometry"));
    private_nh.param("terrain_cloud_topic", terrain_cloud_topic, std::string("/terrain_map"));
    private_nh.param("resolution", resolution, 0.1);
    private_nh.param("log_odds_increase", log_odds_increase, 0.4);
    private_nh.param("log_odds_decrease", log_odds_decrease, 0.2);
    private_nh.param("beam_num", beam_num, 2048);
    private_nh.param("width", width, 40.0);
    private_nh.param("updatetime", updatetime, 1.0);
    private_nh.param("occupancy_threshold", occupancy_threshold, 0.7);
    private_nh.param("vehicle_length", vehicle_length, 1.0);
    private_nh.param("vehicle_width", vehicle_width, 0.5);
    private_nh.param("z_max", z_max, 1.0);
    private_nh.param("z_min", z_min, -1.0);

    sub_cloud = nh.subscribe<sensor_msgs::PointCloud2>(cloud_topic, 1, callbackCloud);
    sub_odom = nh.subscribe<nav_msgs::Odometry>(odom_topic, 1, callbackOdom);
    sub_pose = nh.subscribe<geometry_msgs::PoseStamped>("/gazebo_state/pumbaa_pose", 100, callbackPose);
	//仿真位置真值，实际可以不用
    pub_terrain_cloud = nh.advertise<sensor_msgs::PointCloud2>(terrain_cloud_topic, 1);
    //pub_odom = nh.advertise<nav_msgs::Odometry>("/state_estimate1", 1);//state_estimation
	pub_odom = nh.advertise<nav_msgs::Odometry>("/state_estimation", 1);//state_estimation

    Dynamic.initialization(private_nh, width, resolution, occupancy_threshold, beam_num, log_odds_increase, 
                            log_odds_decrease, updatetime, vehicle_length, vehicle_width, z_max, z_min);
    cout << "Initialize successfully!" << endl;
    ros::Rate rate(100);
    bool status = ros::ok();
    while (status) 
    { 
        ros::spinOnce();
        status = ros::ok();
		rate.sleep();
    }
    return 0;
}
