#ifndef DYNAMIC_DETECTOR_H
#define DYNAMIC_DETECTOR_H

#include <ros/ros.h>
#include <iostream>

#include <nav_msgs/Odometry.h>
#include <nav_msgs/OccupancyGrid.h>

//tf2
#include <tf2/utils.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <tf2_eigen/tf2_eigen.h>

// Eigen
#include <Eigen/Dense>
#include <Eigen/Geometry>
#include <cmath>

// PCL
#include <sensor_msgs/PointCloud2.h>
#include <pcl_ros/transforms.h>
#include <pcl_ros/point_cloud.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_types_conversion.h>
#include <pcl/point_types.h>
#include <pcl/filters/passthrough.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/filters/radius_outlier_removal.h>
#include <pcl/visualization/cloud_viewer.h>
#include <patchworkpp/patchworkpp.hpp>

// OMP
#include <omp.h>

class DynamicCloudDetector
{
public:
    class GridCell
    {
    public:
        GridCell(void)//概率更新函数
        {
            log_odds = 0;
        }
        double get_occupancy(void)
        {
            return 1.0 / (1 + exp(-log_odds));
        }
        double get_log_odds(void)
        {
            return log_odds;
        }
        void add_log_odds(double lo)
        {
            log_odds += lo;
        }

        double log_odds;
    private:
    };
    typedef std::vector<GridCell> OccupancyGridMap;

    int get_index_from_xy(const double x, const double y)
    {
        const int _x = floor(x / resolution_ + 0.5) + grid_width_2_;
        const int _y = floor(y / resolution_ + 0.5) + grid_width_2_;
        return _y * grid_width_ + _x;
    }
    int get_x_index_from_index(const int index)
    {
        return index % grid_width_;
    }
    int get_y_index_from_index(const int index)
    {
        return index / grid_width_;
    }
    bool is_valid_point(double x, double y)
    {
        const int index = get_index_from_xy(x, y);
        if(x < -width_2_ || x > width_2_ || y < -width_2_ || y > width_2_) return false;
        else if(index < 0 || grid_num_ <= index) return false;
        else return true;
    }
    void initialization(ros::NodeHandle nh, double width = 40.0, double resolution = 0.1, double occupancy_threshold = 0.5, 
                        int beam_num = 2048, double log_odds_increase = 0.4, double log_odds_decrease = 0.2, double updatetime = 1.0, 
                        double vehicle_length = 1.0, double vehicle_width = 0.5, double z_max = 1.0, double z_min = -1.0)
    {
        width_ = width;
        resolution_ = resolution;
        occupancy_threshold_ = occupancy_threshold;
        beam_num_ = beam_num;
        log_odds_increase_ = log_odds_increase;
        log_odds_decrease_ = log_odds_decrease;
        updatetime_ = updatetime;
        vehicle_length_ = vehicle_length;
        vehicle_width_ = vehicle_width;
        z_max_ = z_max;
        z_min_ = z_min;
        width_2_ = width_ / 2;
        grid_width_ = width_ / resolution_;
        grid_width_2_ = grid_width_ / 2.0;
        grid_num_ = grid_width_ * grid_width_;
        occupancy_grid_map_.resize(grid_num_);
        PatchworkppGroundSeg.reset(new PatchWorkpp<pcl::PointXYZI>(&nh));
    }
    void input_cloud_to_occupancy_grid_map(const pcl::PointCloud<pcl::PointXYZI>::Ptr& cloud_ptr);
    void devide_cloud(const pcl::PointCloud<pcl::PointXYZI>::Ptr& cloud, pcl::PointCloud<pcl::PointXYZI>::Ptr& dynamic_cloud, pcl::PointCloud<pcl::PointXYZI>::Ptr& static_cloud);
    void transform_occupancy_grid_map(const Eigen::Vector2d& translation, double diff_yaw, OccupancyGridMap& map);
    void set_clear_grid_cells(const std::vector<double>& beam_list, const std::vector<bool>& obstacle_indices, OccupancyGridMap& map);
    void process(const sensor_msgs::PointCloud2::ConstPtr& cloud, const nav_msgs::Odometry::ConstPtr &odom, sensor_msgs::PointCloud2::Ptr& terrain_cloud);

private:
    double width_, width_2_;
    double resolution_;
    int grid_width_, grid_width_2_, grid_num_;
    int beam_num_;
    double log_odds_increase_, log_odds_decrease_, occupancy_threshold_;
    double updatetime_;
    double vehicle_length_, vehicle_width_, z_max_, z_min_;
    OccupancyGridMap occupancy_grid_map_;
    boost::shared_ptr<PatchWorkpp<pcl::PointXYZI>> PatchworkppGroundSeg;
    
};

void DynamicCloudDetector::set_clear_grid_cells(const std::vector<double>& beam_list, const std::vector<bool>& obstacle_indices, OccupancyGridMap& map)
{
    std::vector<bool> clear_indices(grid_num_, false);
    const double beam_angle_resolution = 2.0 * M_PI / (double)beam_num_;
    for(int i = 0; i < beam_num_; i++)
    {
        double direction = i * beam_angle_resolution - M_PI;
        direction = atan2(sin(direction), cos(direction));
        const double c = cos(direction);
        const double s = sin(direction);
        for(double range = 0.0; range < beam_list[i]; range += resolution_)
        {
            const double x = range * c;
            const double y = range * s;
            if(is_valid_point(x, y))
            {
                const int index = get_index_from_xy(x, y);
                if(!obstacle_indices[index]) clear_indices[index] = true;
                else break;
            }
            else break;
        }
    }
    for(int i = 0; i < grid_num_; ++i)
    {
        if(clear_indices[i]) map[i].add_log_odds(-log_odds_decrease_);
    }
}

void DynamicCloudDetector::input_cloud_to_occupancy_grid_map(const pcl::PointCloud<pcl::PointXYZI>::Ptr& cloud_ptr)
{
    std::vector<double>beam_list(beam_num_, sqrt(2) * width_2_);//beam_num_是向量数量，后面是大小
    const double beam_angle_resolution = 2.0 * M_PI / (double)beam_num_;
    const int cloud_size = cloud_ptr->points.size();
    std::vector<bool> obstacle_indices(grid_num_, false);
    for(int i = 0; i < cloud_size; i++)
    {
        const auto& p = cloud_ptr->points[i];
        if(!is_valid_point(p.x, p.y)) continue;
        const double distance = sqrt(p.x * p.x + p.y * p.y);
        const double direction = atan2(p.y, p.x);
        const int beam_index = (direction + M_PI) / beam_angle_resolution;//因为方向角[-pi,pi],+pi为[0,2pi]
        if(0 <= beam_index && beam_index < beam_num_) 
            beam_list[beam_index] = std::min(beam_list[beam_index], distance);//距离取最小值，限制光追范围
        const int index = get_index_from_xy(p.x, p.y);
        if(index < 0 || grid_num_ <= index) continue;
        obstacle_indices[get_index_from_xy(p.x, p.y)] = true;
    }
    for(int i=0;i<grid_num_;i++)
    {
        if(obstacle_indices[i]) occupancy_grid_map_[i].add_log_odds(log_odds_increase_);
    }
    set_clear_grid_cells(beam_list, obstacle_indices, occupancy_grid_map_);
}

void DynamicCloudDetector::transform_occupancy_grid_map(const Eigen::Vector2d& translation, double diff_yaw, OccupancyGridMap& map)
{
    const double dx = translation(0);
    const double dy = translation(1);//表示两次里程计数据之间的位置差值
    const double c_yaw = cos(diff_yaw);
    const double s_yaw = sin(diff_yaw);
    const double dx_grid = dx / resolution_;
    const double dy_grid = dy / resolution_;
    Eigen::Matrix3d affine;
    affine << c_yaw, -s_yaw, dx_grid,
              s_yaw,  c_yaw, dy_grid,
                  0,      0,        1;
    const Eigen::Matrix3d affine_inverse = affine.inverse();
    OccupancyGridMap ogm(grid_num_);
    for(int i = 0;i < grid_num_;i++)//栅格个数
    {
        const double x_i = get_x_index_from_index(i) - grid_width_2_;//将坐标系转移到栅格地图的中心
        const double y_i = get_y_index_from_index(i) - grid_width_2_;
        Eigen::Vector3d ogm_i(x_i, y_i, 1);//齐次坐标
        Eigen::Vector3d map_i = affine_inverse * ogm_i;
        const int x_0 = std::floor(map_i(0));//向下取整
        const int x_1 = x_0 + 1;
        const int y_0 = std::floor(map_i(1));
        const int y_1 = y_0 + 1;
        if(x_0 < -grid_width_2_ || grid_width_2_ <= x_1) continue;
        if(y_0 < -grid_width_2_ || grid_width_2_ <= y_1) continue;
        const int index_0_0 = (y_0 + grid_width_2_) * grid_width_ + x_0 + grid_width_2_;
        const int index_0_1 = (y_1 + grid_width_2_) * grid_width_ + x_0 + grid_width_2_;
        const int index_1_0 = (y_0 + grid_width_2_) * grid_width_ + x_1 + grid_width_2_;
        const int index_1_1 = (y_1 + grid_width_2_) * grid_width_ + x_1 + grid_width_2_;
        const Eigen::Vector2d y_vec(y_1 - map_i(1), map_i(1) - y_0);
        const Eigen::Vector2d x_vec(x_1 - map_i(0), map_i(0) - x_0);
        Eigen::Matrix2d value_mat;
        value_mat << map[index_0_0].get_occupancy(), map[index_1_0].get_occupancy(),
                     map[index_0_1].get_occupancy(), map[index_1_1].get_occupancy();
        const double ogm_value = y_vec.transpose() * value_mat * x_vec;//加权计算占据栅格值
        ogm[i].log_odds = std::log(ogm_value / (1 - ogm_value));
    } 
    map.clear();
    map = ogm;
}

void DynamicCloudDetector::devide_cloud(const pcl::PointCloud<pcl::PointXYZI>::Ptr& cloud, pcl::PointCloud<pcl::PointXYZI>::Ptr& dynamic_cloud, pcl::PointCloud<pcl::PointXYZI>::Ptr& static_cloud)
{
    dynamic_cloud->points.clear();
    static_cloud->points.clear();
    for(const auto& pt : cloud->points)
    {
        if(-width_2_ <= pt.x && pt.x <= width_2_ && -width_2_ <= pt.y && pt.y <= width_2_)
        {
            const int index = get_index_from_xy(pt.x, pt.y);
            if(0 <= index && index < grid_num_)
            {
                const double occupancy = occupancy_grid_map_[index].get_occupancy();
                if(occupancy < occupancy_threshold_) dynamic_cloud->points.push_back(pt);
                else static_cloud->points.push_back(pt);
            }
        }
    }
}

void DynamicCloudDetector::process(const sensor_msgs::PointCloud2::ConstPtr& cloud, const nav_msgs::Odometry::ConstPtr &odom, sensor_msgs::PointCloud2::Ptr& terrain_cloud)
{
    // std::cout << "process" << std::endl;
    double time_taken;
    const double start_time = ros::Time::now().toSec();
    pcl::PointCloud<pcl::PointXYZI>::Ptr cloud_map(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr cloud_vehicle(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr cloud_estimate(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr pc_ground(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr pc_non_ground(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr pc_non_ground1(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr dynamic_obs_cloud(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr static_obs_cloud(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr static_cloud_map(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr static_cloud_multi(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr static_cloud_filtered(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr dynamic_cloud_map(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr dynamic_cloud_filtered(new pcl::PointCloud<pcl::PointXYZI>);
    pcl::PointCloud<pcl::PointXYZI>::Ptr terrain_cloud_map(new pcl::PointCloud<pcl::PointXYZI>);
    //坐标转换
    Eigen::Vector3d t;
    t << odom->pose.pose.position.x, odom->pose.pose.position.y, odom->pose.pose.position.z;
    Eigen::Quaterniond t_Q;
    t_Q.x() = odom->pose.pose.orientation.x;
    t_Q.y() = odom->pose.pose.orientation.y;
    t_Q.z() = odom->pose.pose.orientation.z;
    t_Q.w() = odom->pose.pose.orientation.w;
    Eigen::Affine3d lidar_k2init;
    lidar_k2init = (Eigen::Translation3d (t.cast<double>()) * Eigen::AngleAxisd ((t_Q.toRotationMatrix()).cast<double>()));
    //全局转雷达系
    pcl::fromROSMsg(*cloud, *cloud_map);
    pcl::transformPointCloud(*cloud_map, *cloud_vehicle, lidar_k2init.inverse());
    // *cloud_vehicle = *cloud_map;
    //点云预处理
    int cloudsize = cloud_vehicle->points.size();
    for (int i = 0; i < cloudsize; i++) 
    {
        pcl::PointXYZI point = cloud_vehicle->points[i];
		if((abs(point.x) > vehicle_length_ / 2 || abs(point.y) > vehicle_width_ / 2) && point.z < z_max_ && sqrt(point.x * point.x + point.y * point.y) <= 40) 
        {     
		    cloud_estimate->points.push_back(point);
        }
    }
    //地面分割
    // PatchworkppGroundSeg->estimate_ground(*cloud_estimate, *pc_ground, *pc_non_ground, time_taken);
    if (z_min_ > -0.8)
    {
        for (int i=0; i < int(cloud_estimate->points.size()); i++)
        {
            pcl::PointXYZI point = cloud_estimate->points[i];
            if (point.z < z_min_)
                pc_ground->points.push_back(point);
            else
                pc_non_ground->points.push_back(point);
        }
    }
    vector<vector<double>> stair_detection(360, vector<double>(500, -0.4));
    vector<vector<bool>> stair(360, vector<bool>(500, false));
    for (int i=0; i < int(pc_non_ground->points.size()); i++)
    {
        pcl::PointXYZI point = pc_non_ground->points[i];
        int distance = floor(sqrt(point.x * point.x + point.y * point.y) / resolution_);
        int direction = floor(atan2(point.y, point.x) / 3.1415926 * 180)+180;
        if(direction > 360) direction -= 360;
        if(direction < 0) direction += 360;
        if (point.z > stair_detection[direction][distance]) stair_detection[direction][distance]= point.z;
    }    
    for (int i=0; i < int(stair_detection.size()); i++)
    {
        double last_height = -0.4;
        for (int j=0; j < int(stair_detection[i].size()); j++)
        {
            if(stair_detection[i][j] != -0.4)
            {
                if(stair_detection[i][j] - last_height < 0.4)
                {
                    stair[i][j] = true;
                    last_height = stair_detection[i][j];
                }
                else break;
            }
           
            
        }
    }
    *pc_non_ground1 = *pc_non_ground;
    pc_non_ground->clear();
    for (int i=0; i < int(pc_non_ground1->points.size()); i++)
    {
        pcl::PointXYZI point = pc_non_ground1->points[i];
        int distance = floor(sqrt(point.x * point.x + point.y * point.y) / resolution_);
        int direction = floor(atan2(point.y, point.x) / 3.1415926 * 180)+180;
        if(direction > 360) direction -= 360;
        if(direction < 0) direction += 360;
        if (stair[direction][distance]) pc_ground->push_back(point);
        else pc_non_ground->push_back(point);
    }
    //获取前后两帧位姿航向差
    static Eigen::Vector2d last_odom_position(odom->pose.pose.position.x, odom->pose.pose.position.y);
    static double last_yaw = tf2::getYaw(odom->pose.pose.orientation);
    const Eigen::Vector2d odom_position(odom->pose.pose.position.x, odom->pose.pose.position.y);
    const double yaw = tf2::getYaw(odom->pose.pose.orientation);
    const Eigen::Vector2d diff_odom = Eigen::Rotation2Dd(-last_yaw).toRotationMatrix() * (odom_position - last_odom_position);
    double diff_yaw = yaw - last_yaw;
    diff_yaw = atan2(sin(diff_yaw), cos(diff_yaw));
    //将前一帧栅格转到当前帧
    transform_occupancy_grid_map(-diff_odom, -diff_yaw, occupancy_grid_map_);
    //局部光线追踪
    input_cloud_to_occupancy_grid_map(pc_non_ground);
    devide_cloud(pc_non_ground, dynamic_obs_cloud, static_obs_cloud);
    last_odom_position = odom_position;
    last_yaw = yaw; 
    //将地面点云集合强度值设为0
    for (int i = 0; i < int(pc_ground->points.size()); i++)
    {
        pc_ground->points[i].intensity = 0;
    }
    //将静态障碍物点云强度值设为100
    for (int i=0; i < int(static_obs_cloud->points.size()); i++)
    {
       
        static_obs_cloud->points[i].intensity = 100;
    }
    //设置动态障碍物点云强度为50
    for (int i=0; i < int(dynamic_obs_cloud->points.size()); i++)
    {
       
        dynamic_obs_cloud->points[i].intensity = 50;
    }
    // *static_obs_cloud += *dynamic_obs_cloud;
    // dynamic_obs_cloud->clear();
    //静态点云转全局系
    pcl::transformPointCloud(*pc_ground + *static_obs_cloud, *static_cloud_map, lidar_k2init);
    //静态点云叠加
    static_cloud_map->header.stamp = cloud->header.stamp.toSec() * 1000.0;
    static std::queue<pcl::PointCloud<pcl::PointXYZI>::Ptr> point_queue;
    point_queue.push(static_cloud_map);//将点云入队
    int queue_size = point_queue.size();//队列长度
    for (int i = 0; i < queue_size; i++)
    {
        pcl::PointCloud<pcl::PointXYZI>::Ptr latest_cloud(new pcl::PointCloud<pcl::PointXYZI>);
        latest_cloud = point_queue.front();//获取队列头部的点云消息
        // cout<<"queue_time:"<<cloud->header.stamp.toSec()- latest_cloud->header.stamp /1000.0<<endl;
        if (cloud->header.stamp.toSec()- latest_cloud->header.stamp /1000.0 > updatetime_)
        {
            point_queue.pop();//弹出队列头部的点云消息
        }
        else
        {
            *static_cloud_multi = *static_cloud_multi + *latest_cloud;
            point_queue.pop();
            point_queue.push(latest_cloud);
        }
    }
    //静态点体素滤波
    pcl::VoxelGrid<pcl::PointXYZI> sor;
    sor.setInputCloud(static_cloud_multi);
    sor.setLeafSize(0.1f, 0.1f, 0.1f);
    sor.filter(*static_cloud_filtered);
    //动态点云转全局系
    pcl::transformPointCloud(*dynamic_obs_cloud, *dynamic_cloud_map, lidar_k2init);
    //动态点半径滤波
    pcl::RadiusOutlierRemoval<pcl::PointXYZI> ror;
    ror.setInputCloud(dynamic_cloud_map);
    ror.setRadiusSearch(0.5);
    ror.setMinNeighborsInRadius(2);
    ror.filter(*dynamic_cloud_filtered);
    //可通行区域点云
    *terrain_cloud_map = *static_cloud_filtered + *dynamic_cloud_filtered;
    pcl::toROSMsg(*terrain_cloud_map, *terrain_cloud);
    terrain_cloud->header.frame_id = "map";
    terrain_cloud->header.stamp = ros::Time::now();
    // std::cout << "time: " << ros::Time::now().toSec() - start_time << "[s]" << std::endl;
    //ROS_INFO_STREAM("\033[1;32m" << "Input PointCloud: " << cloud_pcl->size() << " -> Ground: " << pc_ground->size() <<  "/ NonGround: " << pc_non_ground->size()<< " (running_time: " << time_taken << " sec)" << "\033[0m");
}
#endif//DYNAMIC_DETECTOR_H
