#include <rrt_star_global_planner/rrt_star_ros.h>
#include <costmap_2d/costmap_2d_ros.h>
#include <tf2_ros/transform_listener.h>
#include <iostream>
#include <nav_msgs/Path.h>
#include <nav_msgs/Odometry.h>
#include <geometry_msgs/PoseStamped.h>
#include <std_msgs/Int8.h>
#include <geometry_msgs/PoseWithCovarianceStamped.h>
#include <tf/transform_datatypes.h>
#include <tf/transform_broadcaster.h>
#include <rrt_star_global_planner/NavigationResult.h>
#include <rrt_star_global_planner/NavigationTarget.h>
#include <vector>
#include <std_msgs/Bool.h>
#include <geometry_msgs/PoseArray.h>

std_msgs::Int8 safetystop;

namespace RRTstar_planner
{
    using namespace std;
    geometry_msgs::PoseStamped robot_pose;
    geometry_msgs::PoseStamped goal;
    
    //定义RRT规划的类
    class RRTstarPlannerWithCostmap : public RRTstarPlannerROS
    {
        public:
            RRTstarPlannerWithCostmap(std::string name, costmap_2d::Costmap2DROS* cmap);
        private:
            void poseCallback(const geometry_msgs::PoseStamped::ConstPtr& target);
            void webposeCallback(const geometry_msgs::PoseStamped::ConstPtr& target);    
            void odometryCallback(const nav_msgs::Odometry::ConstPtr& goal);
            void replanCallback(const std_msgs::Int8::ConstPtr& replan);
            void testposeCallback(const geometry_msgs::PoseArray::ConstPtr& target);          
            costmap_2d::Costmap2DROS* cmap_;
            ros::Subscriber pose_sub_;
            ros::Subscriber goal_pose_sub_;
            ros::Subscriber test_pose_sub_;
            ros::Subscriber multi_pose_sub_;
            ros::Subscriber Odometry_sub_;
            ros::Subscriber Replan_sub_;
            ros::Publisher goal_pub_;
            ros::Publisher goal_success_pub_;
            ros::Publisher innerpubStop;
    };
    //目标点 from web's callback
    void RRTstarPlannerWithCostmap::webposeCallback(const geometry_msgs::PoseStamped::ConstPtr& target)                                         
    {
        //获取目标点位姿
        
        goal.pose.position.x = target->pose.position.x ;//editing...
        goal.pose.position.y = target->pose.position.y ; //editing...
        //goal.pose.position.z = target->pose.position.z;
        std::cout << "web targt yaw:" << target->pose.orientation.z << ", unit:deg." << std::endl;
        goal.pose.orientation = tf::createQuaternionMsgFromYaw(target->pose.orientation.z);// 这行有点问题应该改成下面这条语句
		//goal.pose.orientation = tf::createQuaternionMsgFromYaw(target->pose.orientation.z * M_PI / 180.0);
        // goal.pose.orientation.x = 0;
        // goal.pose.orientation.y = 0;
        // goal.pose.orientation.z = 0;
        // goal.pose.orientation.w = 1;

        goal_pub_.publish(goal);
        std::vector<geometry_msgs::PoseStamped> path;
        unsigned int mx = 0,my = 0;
        //三维坐标投影到二维栅格地图时报错
        if(!this->costmap_->worldToMap(goal.pose.position.x,goal.pose.position.y,mx,my))
        {
            ROS_ERROR("worldToMap error");
            std::cout << "rrt_stra_plan_node worldToMap error" << std::endl;
            safetystop.data = 1;  
            innerpubStop.publish(safetystop);
            return;//此处不可注释，否则会导致发布空路径而报错
        }
        //目标点位于栅格地图的障碍物上报错
        if(this->costmap_->getCost(mx,my) != costmap_2d::FREE_SPACE)
        {
            ROS_INFO("The target point may be unreachable");
            std::cout << "rrt_stra_plan_node The target point is unreachable." << std::endl;
            safetystop.data = 1;  
            innerpubStop.publish(safetystop);
            return;
        }
        std_msgs::Int8 goalsuccess;

        safetystop.data = 0;  
        innerpubStop.publish(safetystop);
        goalsuccess.data = 5;
        goal_success_pub_.publish(goalsuccess);
        std::cout<<"接受web导航目标成功，正在规划全局路径"<<std::endl;
        //进行RRT全局规划
        makePlan(robot_pose, goal, path);
    }
    void RRTstarPlannerWithCostmap::testposeCallback(const geometry_msgs::PoseArray::ConstPtr& target)                                         
    {
        // std::vector<geometry_msgs::PoseStamped> points;
        std_msgs::Bool msg;
        geometry_msgs::PoseStamped start_point;
        geometry_msgs::PoseStamped target_point;
        //获取目标点位姿
        // points.push_back(*target);
        if(target->poses.size() >= 2)
        {
        start_point.pose.position.x = target->poses[0].position.x ;//editing...
        start_point.pose.position.y = target->poses[0].position.y ; //editing...
        //goal.pose.position.z = target->pose.position.z;
        //goal.pose.orientation = tf::createQuaternionMsgFromYaw(target->yaw);
        start_point.pose.orientation.x = 1;
        start_point.pose.orientation.y = 1;
        start_point.pose.orientation.z = 1;
        start_point.pose.orientation.w = 1;
        target_point.pose.position.x = target->poses[1].position.x ;//editing...
        target_point.pose.position.y = target->poses[1].position.y ; //editing...
        //goal.pose.position.z = target->pose.position.z;
        //goal.pose.orientation = tf::createQuaternionMsgFromYaw(target->yaw);
        target_point.pose.orientation.x = 1;
        target_point.pose.orientation.y = 1;
        target_point.pose.orientation.z = 1;
        target_point.pose.orientation.w = 1;
        }
        
        // goal_pub_.publish(goal);
        std::vector<geometry_msgs::PoseStamped> path;
        unsigned int msx = 0,msy = 0,mtx = 0,mty = 0;
        //三维坐标投影到二维栅格地图时报错
        if(!this->costmap_->worldToMap(start_point.pose.position.x,start_point.pose.position.y,msx,msy) && !this->costmap_->worldToMap(target_point.pose.position.x,target_point.pose.position.y,mtx,mty))
        {
            std::cout << "worldToMap error" << std::endl;
            msg.data = false;
            accessable_pub_.publish(msg);
            std::cout << "test accessable failed." << std::endl;
            return;
        }
        //目标点位于栅格地图的障碍物上报错
        if(this->costmap_->getCost(msx,msy) != costmap_2d::FREE_SPACE && this->costmap_->getCost(mtx,mty) != costmap_2d::FREE_SPACE)
        {
            std::cout << "point is unreachable." << std::endl;
            msg.data = false;
            accessable_pub_.publish(msg);
            std::cout << "test accessable failed." << std::endl;
            return;
        }
        std_msgs::Int8 goalsuccess;
        goalsuccess.data = 5;
        goal_success_pub_.publish(goalsuccess);
        // std::cout<<"接受导航目标成功，正在规划全局路径"<<std::endl;
        //进行RRT全局规划
        makePlan(start_point, target_point, path);
    }

    //目标点信息回调函数
    void RRTstarPlannerWithCostmap::poseCallback(const geometry_msgs::PoseStamped::ConstPtr& target)                                         
    {
        //获取目标点位姿
        
        goal.pose.position.x = target->pose.position.x;
        goal.pose.position.y = target->pose.position.y;
        //std::cout << "handpoint x:[" << goal.pose.position.x << "],y:["  << goal.pose.position.y << "],yaw:["  << target.pose.<< goal.pose << std::endl;
        //goal.pose.position.z = target->pose.position.z;
        //goal.pose.orientation = tf::createQuaternionMsgFromYaw(target->yaw);
        goal.pose.orientation = target->pose.orientation;
        goal_pub_.publish(goal);
        std::vector<geometry_msgs::PoseStamped> path;
        unsigned int mx = 0,my = 0;
        //三维坐标投影到二维栅格地图时报错
        if(!this->costmap_->worldToMap(goal.pose.position.x,goal.pose.position.y,mx,my))
        {
            std::cout << "worldToMap error" << std::endl;
            safetystop.data = 1;  
            innerpubStop.publish(safetystop);
            return;
        }
        //目标点位于栅格地图的障碍物上报错
        if(this->costmap_->getCost(mx,my) != costmap_2d::FREE_SPACE)
        {
            std::cout << "The target point is unreachable." << std::endl;
            safetystop.data = 1;  
            innerpubStop.publish(safetystop);
            return;
        }
        std_msgs::Int8 goalsuccess;
        safetystop.data = 0;  
        innerpubStop.publish(safetystop);
        goalsuccess.data = 5;
        goal_success_pub_.publish(goalsuccess);
        std::cout<<"接受手动导航目标成功，正在规划全局路径"<<std::endl;
        //进行RRT全局规划
        makePlan(robot_pose, goal, path);
    }

    
    
    //载体当前位姿信息回调函数
    void RRTstarPlannerWithCostmap::odometryCallback(const nav_msgs::Odometry::ConstPtr& odom)
    {
        robot_pose.header.seq = odom->header.seq;
        robot_pose.header.stamp = odom->header.stamp;
        robot_pose.header.frame_id = "map";
        robot_pose.pose.position.x=odom->pose.pose.position.x;
        robot_pose.pose.position.y=odom->pose.pose.position.y;
        robot_pose.pose.position.z=odom->pose.pose.position.z;
        robot_pose.pose.orientation.x=odom->pose.pose.orientation.x;
        robot_pose.pose.orientation.y=odom->pose.pose.orientation.y;
        robot_pose.pose.orientation.z=odom->pose.pose.orientation.z;
        robot_pose.pose.orientation.w=odom->pose.pose.orientation.w;
    }
    void RRTstarPlannerWithCostmap::replanCallback(const std_msgs::Int8::ConstPtr& replan)
    {
        if (replan->data == 1)
        {
            std::vector<geometry_msgs::PoseStamped> path;
            unsigned int mx = 0,my = 0;
            //三维坐标投影到二维栅格地图时报错
            if(!this->costmap_->worldToMap(goal.pose.position.x,goal.pose.position.y,mx,my))
            {
                std::cout << "worldToMap error" << std::endl;
                return;
            }
            //目标点位于栅格地图的障碍物上报错
            if(this->costmap_->getCost(mx,my) != costmap_2d::FREE_SPACE)
            {
                std::cout << "The target point is unreachable." << std::endl;
                return;
            }
            std::cout<<"重规划中"<<std::endl;
            //进行RRT全局规划
            makePlan(robot_pose, goal, path);
        }
    }
    //RRT规划节点初始化
    RRTstarPlannerWithCostmap::RRTstarPlannerWithCostmap(std::string name, costmap_2d::Costmap2DROS* cmap) :
            RRTstarPlannerROS(name, cmap)
    {
        ros::NodeHandle private_nh("move_base_simple");
        cmap_ = cmap;
        //订阅导航目标点及导航参数信息
        //pose_sub_ = private_nh.subscribe<rrt_star_global_planner::NavigationTarget>("/navigation_target", 1, &RRTstarPlannerWithCostmap::poseCallback, this);
        //pose_sub_ = private_nh.subscribe<geometry_msgs::PoseStamped>("/move_base_simple/goal", 1, &RRTstarPlannerWithCostmap::poseCallback, this);
        pose_sub_ = private_nh.subscribe<geometry_msgs::PoseStamped>("/move_base_simple/goal", 1, &RRTstarPlannerWithCostmap::poseCallback, this);
        //订阅云端目标点
        goal_pose_sub_ = private_nh.subscribe<geometry_msgs::PoseStamped>("/web_goal_pose", 1, &RRTstarPlannerWithCostmap::webposeCallback, this);
        //
        test_pose_sub_ = private_nh.subscribe<geometry_msgs::PoseArray>("/test_poses", 1, &RRTstarPlannerWithCostmap::testposeCallback, this);
        //订阅载体当前位姿信息
        Odometry_sub_ = private_nh.subscribe<nav_msgs::Odometry>("/Odometry", 1, &RRTstarPlannerWithCostmap::odometryCallback, this);
        //订阅重规划信息
        Replan_sub_ = private_nh.subscribe<std_msgs::Int8> ("/replan", 1, &RRTstarPlannerWithCostmap::replanCallback, this);
        //发布导航目标点在rviz中的显示信息
        goal_pub_= private_nh.advertise<geometry_msgs::PoseStamped>("/target_goal", 1);

        accessable_pub_ = private_nh.advertise<std_msgs::Bool>( "/ifAccessable", 1 ); 
        //发布目标点下发成功信息
        goal_success_pub_= private_nh.advertise<std_msgs::Int8>("/goal_success", 1);

        innerpubStop = private_nh.advertise<std_msgs::Int8> ("/istop", 1 );

        
    }
}

int main(int argc, char** argv)
{
    ros::init(argc, argv, "rrt_star_planner");
    tf2_ros::Buffer buffer(ros::Duration(10));
    tf2_ros::TransformListener tf(buffer);
    costmap_2d::Costmap2DROS gcm("global_costmap", buffer);
    RRTstar_planner::RRTstarPlannerWithCostmap pppp("RRTstarPlannerROS", &gcm);
    
    ros::spin();
    return 0;
}

