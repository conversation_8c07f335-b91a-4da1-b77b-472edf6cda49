<?xml version="1.0"?>
<!-- 此文件单独测试rrt路径规划使用 -->
<launch>
  <arg name="rviz" default="false" />
  <!-- 启动地图 -->
  <node pkg="map_server" name="map_server" type="map_server" args="/home/<USER>/NR_Navigation/src/rrt_star_global_planner/maps/map.yaml"/>

  <!-- 启动map->odom静态TF发布 -->
  <node pkg="tf" type="static_transform_publisher" name="map_odom_broadcaster" args="0 0 0 0 0 0 1 /map /odom 100" />
  
  <node pkg="tf" type="static_transform_publisher" name="map_base_transform" args="0 0 0 0 0 0 /map /base_footprint 100"/>

  <!--启动rrt全局规划器-->
  <node pkg="rrt_star_global_planner" type="rrt_star_planner" respawn="false" name="rrt_star_planner" output="screen">
    <rosparam file="$(find rrt_star_global_planner)/params/test_rrt_star_planner.yaml" command="load" />
  </node>
  <group if="$(arg rviz)">
  <node name="rviznavi" pkg="rviz" type="rviz" args="-d $(find rrt_star_global_planner)/rviz/rrt_star_global_planner.rviz"/>
  </group>
</launch>


