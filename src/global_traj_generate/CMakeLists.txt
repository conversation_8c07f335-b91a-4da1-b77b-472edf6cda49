cmake_minimum_required(VERSION 2.8.3)
project(global_traj_generate)

## Compile as C++11, supported in ROS Kinetic and newer
# add_compile_options(-std=c++11)

## Find catkin macros and libraries
## if COMPONENTS list like find_package(catkin REQUIRED COMPONENTS xyz)
## is used, also find other catkin packages
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  geometry_msgs
  nav_msgs
  sensor_msgs
  tf
  message_generation
)

add_message_files(
  FILES
  NavigationResult.msg
  NavigationTarget.msg
 )
 
 generate_messages(
  DEPENDENCIES
  std_msgs
 )


find_package(Eigen3 REQUIRED)
find_package(PCL REQUIRED)

catkin_package(
  CATKIN_DEPENDS geometry_msgs nav_msgs roscpp rospy std_msgs message_runtime
  DEPENDS EIGEN3 PCL
)

include_directories(
  ${catkin_INCLUDE_DIRS}
  ${PCL_INCLUDE_DIRS}
)

#add_subdirectory(src/lib)

add_executable(global_traj_generate src/global_traj_generate.cpp)

TARGET_LINK_LIBRARIES(global_traj_generate ${PCL_LIBRARIES} 
     ${catkin_LIBRARIES} )

add_dependencies(global_traj_generate ${PROJECT_NAME}_generate_messages_cpp)
